<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillReimbursementApplyRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        bill_id,
        apply_user_id,
        client_id,
        pay_year,
        pay_month,
        apply_date,
        title,
        amount,
        memo,
        approve_status,
        notice_roles,
        is_delete ,
        created_by ,
        last_modified_by ,
        created_date ,
        last_modified_date,
        accounting_voucher_status,
        nc_voucher
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBillReimbursementApply">
        <id column="id" property="id"/>
        <result column="bill_id" property="billId"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="client_id" property="clientId"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_month" property="payMonth"/>
        <result column="apply_date" property="applyDate"/>
        <result column="title" property="title"/>
        <result column="amount" property="amount"/>
        <result column="memo" property="memo"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="notice_roles" property="noticeRoles"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
        <result column="accounting_voucher_status" property="accountingVoucherStatus"/>
        <result column="nc_voucher" property="ncVoucher"/>
    </resultMap>

    <select id="selectExpenditure" resultType="cn.casair.dto.HrBillReimbursementApplyDTO">
        WITH a AS (
            SELECT
                client_id,
                STR_TO_DATE( CONCAT( pay_year, '-', pay_month, '-', 01 ), '%Y-%m-%d' ) payment_date,
                amount
            FROM
            hr_bill_reimbursement_apply
            WHERE
                is_delete = 0
                AND approve_status = 8
        ) SELECT
            a.client_id,
            ANY_VALUE ( hc.client_name ) client_name,
        SUM( a.amount ) amount
        FROM
            a
            LEFT JOIN hr_client hc ON hc.id = a.client_id
            AND hc.is_delete = 0
        <where>
            <if test="permissionClient!=null and permissionClient.size>0">
                AND hc.id IN
                <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.clientIds!=null and params.clientIds.size>0">
                AND a.client_id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.paymentDateStart!=null">
                AND a.payment_date &gt;= #{params.paymentDateStart}
            </if>
            <if test="params.paymentDateEnd!=null">
                AND a.payment_date &lt;= #{params.paymentDateEnd}
            </if>
        </where>
        GROUP BY
            a.client_id
    </select>

    <select id="queryForPage" resultType="cn.casair.dto.HrBillReimbursementApplyDTO">
        select
            *
        from (
        SELECT
        ha.*
        ,su.real_name AS apply_real_name
        FROM hr_bill_reimbursement_apply ha
        LEFT JOIN sys_user su ON ha.apply_user_id = su.id
        WHERE ha.is_delete = 0 AND ha.is_show = 0
        <if test="param.curUserStartStatus != null">
            and ha.approve_status >= #{param.curUserStartStatus}
        </if>
        <if test="param.allowClientIds != null and param.allowClientIds.size > 0">
            and ha.client_id in
            <foreach collection="param.allowClientIds" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param.curUserId != null and param.curUserId != ''">
            or (ha.apply_user_id = #{param.curUserId} and ha.is_delete = 0)
        </if>
        ) ha
        where ha.is_delete = 0 AND ha.is_show = 0
        <choose>
            <when test="param.reimbursementState == 1">
                and ha.reimbursement_state = #{param.reimbursementState} AND ha.approve_status > 0
            </when>
            <otherwise>
                and (ha.reimbursement_state = #{param.reimbursementState} OR ha.approve_status = 0)
            </otherwise>
        </choose>
        <if test="param.title != null and param.title != '' ">
            and ha.title like concat('%', #{param.title}, '%')
        </if>
        <if test="param.applyRealName != null and param.applyRealName != '' ">
            and ha.apply_real_name = #{param.applyRealName}
        </if>
        <if test="param.applyDate != null">
            and ha.apply_date = #{param.applyDate}
        </if>
        <if test="param.approveStatus != null">
            and ha.approve_status = #{param.approveStatus}
        </if>
        <if test="param.approveStatusList != null and param.approveStatusList.size() > 0">
            and ha.approve_status in
            <foreach collection="param.approveStatusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param.amountQuery != null and param.amountQuery.size() > 0">
            AND ha.amount BETWEEN #{param.amountStart} AND #{param.amountEnd}
        </if>
        <if test="param.accountingVoucherStatus != null ">
            and ha.accounting_voucher_status = #{param.accountingVoucherStatus}
        </if>
    </select>

    <select id="getById" resultType="cn.casair.dto.HrBillReimbursementApplyDTO">
        SELECT ha.*, hc.client_name, sr.real_name as apply_real_name
            FROM hr_bill_reimbursement_apply ha
            LEFT JOIN hr_client hc ON ha.client_id = hc.id
            left join sys_user sr on ha.apply_user_id = sr.id
            WHERE ha.is_delete = 0  AND ha.id = #{applyId}
    </select>

    <select id="selectByIdList" resultType="cn.casair.dto.HrBillReimbursementApplyDTO">
        SELECT
            ha.*,
            hc.client_name,
            sr.real_name AS apply_real_name
        FROM
            hr_bill_reimbursement_apply ha
            LEFT JOIN hr_client hc ON ha.client_id = hc.id
            LEFT JOIN sys_user sr ON ha.apply_user_id = sr.id
        WHERE
            ha.is_delete = 0
            AND ha.id IN
            <foreach collection="idList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
    </select>

    <select id="findAccountByApplyId" resultType="cn.casair.dto.HrBillReimbursementClientDTO">
        SELECT
            brc.account_id                  AS accountId,
            ANY_VALUE(hpa.account_number)   AS accountNumber,
            COUNT( brc.client_id )          AS clientNum,
            SUM( brc.amount )               AS amount
        FROM
            hr_bill_reimbursement_client brc
            LEFT JOIN hr_platform_account hpa ON brc.account_id = hpa.id
        WHERE brc.is_delete = 0 AND brc.apply_id = #{applyId}
        GROUP BY brc.account_id
    </select>

    <select id="findBillByApplyId" resultType="cn.casair.dto.HrBillReimbursementClientDTO">
        SELECT
            hc.client_name AS clientName,
            hfr.title AS billTitle,
            brc.*
        FROM
            hr_bill_reimbursement_client brc
            LEFT JOIN hr_fee_review hfr ON brc.fee_review_id = hfr.id
            LEFT JOIN hr_client hc ON brc.client_id = hc.id
        WHERE
            brc.is_delete = 0
            AND brc.apply_id = #{applyId}
    </select>

    <select id="findSpecialClientInfo" resultType="cn.casair.dto.HrBillReimbursementClientDTO">
        SELECT
            hc.client_name,
            hbrc.*
        FROM
            hr_bill_reimbursement_client hbrc
            LEFT JOIN hr_client hc ON hbrc.client_id = hc.id
        WHERE
            hbrc.is_delete = 0 AND hbrc.apply_id = #{applyId}
    </select>

    <select id="getByBillId" resultType="cn.casair.dto.HrBillReimbursementApplyDTO">
        SELECT
            hbra.*
        FROM
            hr_bill_reimbursement_client hbrc
            LEFT JOIN hr_bill_reimbursement_apply hbra ON hbrc.apply_id = hbra.id
        WHERE
            hbrc.is_delete = 0 AND hbrc.bill_id = #{billId}
    </select>

    <select id="selectBillId" resultType="java.lang.String">
        SELECT
            DISTINCT bill_id
        FROM
            hr_bill_reimbursement_apply hbra
            LEFT JOIN hr_bill_reimbursement_apply_detail hbrad ON hbrad.apply_id = hbra.id AND hbrad.is_delete = 0
        WHERE
            hbra.is_delete = 0
            AND hbra.reimbursement_state = 1
            AND hbra.approve_status NOT IN ( 9, 10, 11 )
            AND hbra.bill_id IS NOT NULL
            AND hbrad.invoice_type = #{accountType}
            AND hbra.pay_year = #{payYear}
	        AND hbra.pay_month = #{payMonth}
    </select>

    <select id="getAccountType" resultType="cn.casair.dto.HrBillReimbursementClientDTO">
        SELECT
            hbra.account_type,
            SUM(hbrc.amount) AS amount
        FROM
            hr_bill_reimbursement_client hbrc
            LEFT JOIN hr_bill_reimbursement_apply hbra ON hbrc.apply_id = hbra.id
        WHERE
            hbrc.is_delete = 0
            AND hbrc.bill_id IS NOT NULL
            AND hbra.account_type IS NOT NULL
            AND hbra.approve_status NOT IN ( 9, 10, 11 )
            AND hbra.pay_year = #{payYear}
            AND hbra.pay_month = #{payMonth}
            AND hbrc.bill_id IN
            <foreach collection="billIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
            GROUP BY hbra.account_type
    </select>

    <select id="selectClientApply" resultType="java.lang.String">
        SELECT
            DISTINCT hbrc.fee_review_id
        FROM
            hr_bill_reimbursement_client hbrc
            LEFT JOIN hr_bill_reimbursement_apply hbra ON hbrc.apply_id = hbra.id
        WHERE
            hbrc.is_delete = 0
            AND hbrc.fee_review_id IS NOT NULL
            AND hbra.account_type IS NOT NULL
            AND hbra.account_type = #{accountType}
            AND hbra.approve_status NOT IN ( 9, 10, 11 )
            AND hbra.pay_year = #{payYear}
            AND hbra.pay_month = #{payMonth}
    </select>

    <select id="findCommonApplyList" resultType="cn.casair.dto.HrBillReimbursementApplyDTO">
        SELECT
            hbra.*,
            ANY_VALUE(hc.client_name) client_name,
            GROUP_CONCAT(DISTINCT hpa.account_number) AS account_number
        FROM
            hr_bill_reimbursement_apply hbra
        LEFT JOIN hr_client hc ON hbra.client_id = hc.id
        LEFT JOIN hr_bill_reimbursement_client hbrc ON hbrc.apply_id = hbra.id
        LEFT JOIN hr_platform_account hpa ON hbrc.account_id = hpa.id
        WHERE hbra.is_delete = 0 AND hbra.reimbursement_state = #{param.reimbursementState}
        <if test="param.allowClientIds != null and param.allowClientIds.size > 0">
            AND ( hbra.client_id IN
            <foreach collection="param.allowClientIds" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
            OR hbra.client_id IS NULL)
        </if>
        <if test="param.approveStatusList != null and param.approveStatusList.size() > 0">
            AND hbra.approve_status IN
            <foreach collection="param.approveStatusList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        GROUP BY hbra.id
        ORDER BY hbra.created_date DESC LIMIT 5
    </select>

    <update id="updateApproveStatus">
        UPDATE hr_bill_reimbursement_apply SET approve_status = #{approveStatus}, reimbursement_lock_state = #{lockState}, last_modified_date = now() WHERE  bill_id = #{id}
    </update>

    <update id="updateLockState">
        UPDATE hr_bill_reimbursement_apply SET reimbursement_lock_state = #{lockState}, last_modified_date = now() WHERE id = #{id}
    </update>

    <update id="updateClientLock">
        UPDATE hr_bill_reimbursement_client
        <set>
            <if test="realSalaryLock != null">
                real_salary_lock = #{realSalaryLock},
            </if>
            <if test="socialSecurityLock != null">
                social_security_lock = #{socialSecurityLock},
            </if>
            <if test="medicalInsuranceLock != null">
                medical_insurance_lock = #{medicalInsuranceLock},
            </if>
            <if test="accumulationFoundLock != null">
                accumulation_found_lock = #{accumulationFoundLock},
            </if>
        </set>
        WHERE is_delete = 0 AND id IN
        <foreach collection="ids" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </update>

    <update id="delApplyClientId">
        UPDATE hr_bill_reimbursement_client SET is_delete = 1 WHERE apply_id = #{applyId}
    </update>

</mapper>
