<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrClientRepository">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, unit_number, parent_id, client_name, business_type, address, enterprise_nature, established_date, specialized_id, provident_fund_account_id,
        medical_insurance_account_id, social_security_account_id, payroll_account_id, provident_fund_type_id, social_security_type_id, remarks, `status`,
        industry, client_pay, pay_date, is_delete, created_by, last_modified_by, created_date, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrClient">
        <id column="id" property="id"/>
        <result column="unit_number" property="unitNumber"/>
        <result column="parent_id" property="parentId"/>
        <result column="client_name" property="clientName"/>
        <result column="business_type" property="businessType"/>
        <result column="address" property="address"/>
        <result column="enterprise_nature" property="enterpriseNature"/>
        <result column="specialized_id" property="specializedId"/>
        <result column="provident_fund_account_id" property="providentFundAccountId"/>
        <result column="medical_insurance_account_id" property="medicalInsuranceAccountId"/>
        <result column="social_security_account_id" property="socialSecurityAccountId"/>
        <result column="payroll_account_id" property="payrollAccountId"/>
        <result column="provident_fund_type_id" property="providentFundTypeId"/>
        <result column="social_security_type_id" property="socialSecurityTypeId"/>
        <result column="remarks" property="remarks"/>
        <result column="status" property="status"/>
        <result column="industry" property="industry"/>
        <result column="client_pay" property="clientPay"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <select id="selectTaxSummaryClient" resultType="cn.casair.dto.report.TaxSummaryClientDTO">
        SELECT
            <if test="params.exhibitionType==1">
                CONCAT( fr.pay_year, '-' ,IF ( fr.pay_monthly &gt; 9, fr.pay_monthly, CONCAT( '0', fr.pay_monthly ))) dateStr,
            </if>
            <if test="params.exhibitionType==2">
                CONCAT( fr.pay_year, '年第', FLOOR( ( fr.pay_monthly + 2 ) / 3 ), '季度' ) dateStr,
            </if>
            <if test="params.exhibitionType==3">
                CONCAT( fr.pay_year, '年', IF ( fr.pay_monthly &lt; 7, '上', '下'), '半年度') dateStr,
            </if>
            <if test="params.exhibitionType==4">
                CONCAT( fr.pay_year, '年' )        dateStr,
            </if>
            c.client_name,
            c.parent_id,
            IFNULL( SUM( bt.personal_tax_total ), 0 ) staffTaxTotal
        FROM hr_client c
        LEFT JOIN hr_fee_review fr ON fr.client_id = c.id
        LEFT JOIN hr_bill_total bt ON fr.id = bt.bill_id
        WHERE
            c.is_delete = 0
            AND fr.status = 1
            AND CONCAT( fr.pay_year, '-', IF ( fr.pay_monthly &lt; 10, CONCAT( '0', fr.pay_monthly ), fr.pay_monthly )) &gt;= #{params.exhibitionDateStart}
            AND CONCAT( fr.pay_year, '-', IF ( fr.pay_monthly &lt; 10, CONCAT( '0', fr.pay_monthly ), fr.pay_monthly )) &lt;= #{params.exhibitionDateEnd}
            <if test="params.clientIds != null and params.clientIds.size() > 0">
                AND c.id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
        GROUP BY
            c.id,
            <if test="params.exhibitionType==1">
                fr.pay_year,
                fr.pay_monthly
            </if>
            <if test="params.exhibitionType==2 || params.exhibitionType==3">
                dateStr
            </if>
            <if test="params.exhibitionType==4">
                fr.pay_year
            </if>
        ORDER BY dateStr DESC
    </select>

    <select id="selectReducedClient" resultType="cn.casair.dto.report.ClientInfoChangeDetailDTO">
        SELECT
            *
        FROM (
            SELECT
                hc.client_name,
                hc.created_date dateTimeStr,
                1 reason
            FROM
                hr_client hc
            WHERE
                hc.is_delete = 1
                <if test="params.exhibitionDateStart!=null and params.exhibitionDateStart!=''">
                    AND DATE_FORMAT(hc.created_date, '%Y-%m') &gt;= #{params.exhibitionDateStart}
                </if>
                <if test="params.exhibitionDateEnd!=null and params.exhibitionDateEnd!=''">
                    AND DATE_FORMAT(hc.created_date, '%Y-%m') &lt;= #{params.exhibitionDateEnd}
                </if>
            UNION ALL
            SELECT
                hc.client_name,
                hp.agreement_end_date dateTimeStr,
                2 reason
            FROM
                hr_protocol hp
                LEFT JOIN hr_client hc ON hc.id = hp.client_id
            WHERE
                hp.is_delete = 0
                AND hp.states = 2
                <if test="params.exhibitionDateStart!=null and params.exhibitionDateStart!=''">
                    AND DATE_FORMAT(hp.agreement_end_date, '%Y-%m') &gt;= #{params.exhibitionDateStart}
                </if>
                <if test="params.exhibitionDateEnd!=null and params.exhibitionDateEnd!=''">
                    AND DATE_FORMAT(hp.agreement_end_date, '%Y-%m') &lt;= #{params.exhibitionDateEnd}
                </if>
            ) as a
        ORDER BY
            a.dateTimeStr DESC
    </select>

    <select id="selectIncreasedClient" resultType="cn.casair.dto.report.ClientInfoChangeDetailDTO">
        SELECT
            hc.client_name,
            hc.created_date dateTimeStr
        FROM
            hr_client hc
        WHERE
            hc.is_delete = 0
        <if test="params.exhibitionDateStart!=null and params.exhibitionDateStart!=''">
            AND DATE_FORMAT(hc.created_date, '%Y-%m') &gt;= #{params.exhibitionDateStart}
        </if>
        <if test="params.exhibitionDateEnd!=null and params.exhibitionDateEnd!=''">
            AND DATE_FORMAT(hc.created_date, '%Y-%m') &lt;= #{params.exhibitionDateEnd}
        </if>
    </select>

    <select id="getRootParentClient" resultType="cn.casair.domain.HrClient">
        WITH RECURSIVE temp AS (
            SELECT * FROM hr_client WHERE id = #{clientId}
            UNION ALL
            SELECT c.* FROM hr_client c, temp t WHERE	c.id = t.parent_id
        )
        SELECT * FROM temp WHERE parent_id = '0'
    </select>

    <select id="getSubordinateClient" resultType="cn.casair.dto.HrClientDTO">
         WITH RECURSIVE temp AS (
            SELECT * FROM hr_client WHERE id = #{clientId}
            UNION ALL
            SELECT c.* FROM hr_client c, temp t WHERE c.parent_id = t.id
        )
        SELECT * FROM temp WHERE is_delete = 0
    </select>

    <select id="getClientByProvidentFundTypeId" resultType="cn.casair.domain.HrClient">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_client
        WHERE is_delete = 0
        AND status = 0
    </select>

    <select id="selectClientUnitNumber" resultType="cn.casair.domain.HrClient">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_client
        WHERE is_delete = 0
        AND unit_number = #{unitNumber}
    </select>

    <select id="getClientNameList" resultType="cn.casair.domain.HrClient">
        SELECT id,
               client_name
        FROM hr_client
        WHERE is_delete = 0
    </select>

    <select id="getselectHrClient" resultType="cn.casair.dto.HrClientDTO">
        select id, parent_id, client_name
        from hr_client
        WHERE is_delete = 0
        <if test="ids!=null and ids.size() > 0">
            AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            AND client_name like concat('%',#{clientName},'%')
        </if>
    </select>

    <select id="getHrSocialSelectSecurity" resultType="cn.casair.dto.HrSocialSecurityDTO">
        SELECT id, social_security_name
        from hr_social_security
        where is_delete = 0
    </select>

    <select id="getgetHrAccumulationSelectSecurity" resultType="cn.casair.dto.HrAccumulationFundDTO">
        SELECT id, type_name
        from hr_accumulation_fund
        where is_delete = 0
    </select>


    <select id="getHrPlatformAccount" resultType="cn.casair.dto.HrPlatformAccountDTO">
        SELECT id, account_number, platform_type
        from hr_platform_account
        where is_delete = 0

    </select>

    <select id="getClientSelect" resultType="cn.casair.dto.HrClientDTO">
        SELECT hc.*,
               hp.agreement_start_date AS agreementStartDate,
               hp.agreement_end_date   AS agreementEndDate
        FROM hr_client hc
                 LEFT JOIN hr_protocol hp ON hc.id = hp.client_id
        WHERE hc.is_delete = 0
          and status = 0
    </select>

    <select id="page" resultType="cn.casair.dto.HrClientDTO">
        SELECT
        *
        FROM
        hr_client hc
        LEFT JOIN (
        SELECT
        any_value ( id ) protocolId,
        any_value ( client_id ) client_id,
        any_value ( agreement_end_date ) agreement_end_date,
        any_value ( agreement_start_date ) agreement_start_date,
        any_value ( agreement_type ) agreementType,
        any_value ( agreement_number ) agreementNumber,
        max( agreement_start_date )
        FROM
        hr_protocol
        WHERE
        is_delete = 0
        and use_status=1

        GROUP BY
        client_id
        ) AS a ON a.client_id = hc.id
        LEFT JOIN sys_user su on su.id=hc.user_id
        WHERE
        hc.is_delete = 0
        <if test="param1.ids!=null and param1.ids.size() > 0">
            AND hc.id IN
            <foreach collection="param1.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.idList!=null and param1.idList.size() > 0">
            AND hc.id IN
            <foreach collection="param1.idList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.clientIdList!=null and param1.clientIdList.size() > 0">
            AND hc.id IN
            <foreach collection="param1.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.clientIds!=null and param1.clientIds.size() > 0">
            AND hc.id IN
            <foreach collection="param1.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="param1.agreementTypeList!=null and param1.agreementTypeList.size() > 0">
            AND a.agreementType IN
            <foreach collection="param1.agreementTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>


        <if test="param1.enterpriseNatureList!=null and param1.enterpriseNatureList.size() > 0">
            AND hc.enterprise_nature IN
            <foreach collection="param1.enterpriseNatureList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>

        <if test="param1.specializedIdList!=null and param1.specializedIdList.size() > 0">
            and hc.specialized_id IN
            <foreach collection="param1.specializedIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>

        <if test="param1.unitNumber!=null and param1.unitNumber!=''">
            and hc.unit_number like concat('%',#{param1.unitNumber},'%')
        </if>

        <if test="param1.id!=null and param1.id!=''">
            and hc.id=#{param1.id}
        </if>

        <if test="param1.providentFundAccountId != null and param1.providentFundAccountId != ''">
            and hc.provident_fund_account_id=#{param1.providentFundAccountId}
        </if>

        <if test="param1.socialSecurityTypeId != null and param1.socialSecurityTypeId != ''">
            and hc.social_security_type_id=#{param1.socialSecurityTypeId}
        </if>

        <if test="param1.providentFundTypeId != null and param1.providentFundTypeId != ''">
            and hc.provident_fund_type_id=#{param1.providentFundTypeId}
        </if>
        <if test="param1.customerType!='' and param1.customerType!=null ">
            and a.states=#{param1.customerType}
        </if>
        <if test="param1.agreementStartDate!=null ">
            AND a.agreement_start_date <![CDATA[ >= ]]> #{param1.agreementStartDate}
        </if>
        <if test="param1.agreementStartDateend!=null ">
            AND a.agreement_start_date <![CDATA[ <= ]]> #{param1.agreementStartDateend}
        </if>
        <if test="param1.agreementEndDate!=null ">
            AND a.agreement_end_date <![CDATA[ >= ]]> #{param1.agreementEndDate}
        </if>
        <if test="param1.agreementEndDateend!=null ">
            AND a.agreement_end_date <![CDATA[ <= ]]> #{param1.agreementEndDateend}
        </if>

        <if test="param1.field != null and param1.field != '' and param1.order != null and param1.order != ''">
            order by ${param1.field} ${param1.order}
        </if>
        <if test="param1.field == null  and param1.order == null ">
        order by  hc.created_date desc
        </if>
    </select>
    <select id="selectclientsum" resultType="int">
        SELECT COUNT(id) as peoplesum
        from hr_talent_staff
        WHERE client_id = #{id}
          AND is_delete = 0
    </select>


    <select id="getclientsspecialized" resultType="cn.casair.dto.UserDTO">
        SELECT su.id,
               real_name,
               user_name,
               phone
        FROM sys_role sr
                 LEFT JOIN sys_user_role sur ON sr.id = sur.role_id
                 LEFT JOIN sys_user su ON sur.user_id = su.id
        WHERE sr.role_key = "customer_service_staff"
          AND su.is_delete = 0
          AND su.user_status = 1
        GROUP BY id
    </select>

    <select id="getunitNumber" resultType="int">
        SELECT COUNT(id)
        FROM hr_client
        WHERE unit_number = #{unitNumber}
          AND is_delete = 0
          and status = 0
    </select>


    <insert id="insertuser">
        INSERT INTO sys_user (id, user_name, password)
        VALUES (#{id}, #{userName}, #{password})
    </insert>

    <select id="selectspecialized" resultType="java.lang.String">
        SELECT real_name as specialized
        FROM sys_user
        WHERE id = #{specializedId}
          AND is_delete = 0
          AND user_status = 1;
    </select>

    <select id="getClientId" resultType="cn.casair.dto.HrClientDTO">
        SELECT *
        from hr_client
        WHERE id = #{id}
          and is_delete = 0
    </select>

    <select id="getprotoco" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT *
        FROM hr_protocol
        WHERE client_id = #{id}
          AND use_status = 1
          AND is_delete = 0
        ORDER BY agreement_start_date DESC
    </select>

    <select id="getHrDocking" resultType="cn.casair.dto.HrDockingDTO">
        SELECT *
        from hr_docking
        WHERE protocol_id = #{protocolId}
          and is_delete = 0
    </select>


    <select id="getclientUser" resultType="int">
        SELECT COUNT(id)
        FROM sys_user
        WHERE user_name = #{userName}
          and is_delete = 0
    </select>

    <insert id="insertuserrole">
        INSERT INTO sys_user_role (user_id, role_id)
        VALUES (#{userId}, #{roleId})
    </insert>

    <select id="getusername" resultType="cn.casair.dto.UserDTO">
        SELECT su.*
        FROM hr_client hc
                 LEFT JOIN   sys_user su on hc.user_id=su.id
        WHERE
            hc.id = #{id}
          AND su.is_delete = 0

        GROUP BY su.id
    </select>

    <select id="getspecializedphone" resultType="java.lang.String">
        SELECT phone as specializedphone
        from sys_user
        WHERE id = #{specializedId}
          AND is_delete = 0
          AND user_status = 1
    </select>

    <select id="getTalentStaff" resultType="cn.casair.dto.HrTalentStaffDTO">
        SELECT *
        from hr_talent_staff
        WHERE client_id = #{id}
          AND is_delete = 0
    </select>

    <select id="selectprotocol" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT
        any_value(id) id,
        any_value ( agreement_number ) agreement_number,
        any_value ( agreement_title ) agreement_title,
        any_value ( agreement_end_date ) agreement_end_date,
        any_value ( settlement_method ) settlement_method,
        any_value ( agreement_owned_customer ) agreement_owned_customer,
        any_value ( service_charge_type ) service_charge_type,
        any_value ( agreement_start_date ) agreement_start_date,
        any_value ( states ) states,
        any_value ( last_modified_date ) last_modified_date,
        any_value ( client_id ) client_id,
        any_value ( reason_termination_agreement ) reason_termination_agreement,
        any_value ( appendix_id ) appendix_id,
        any_value ( remark ) remark,
        any_value ( created_date ) created_date,
        any_value ( agreement_type ) agreementType,
        any_value ( agreement_termination_date ) agreement_termination_date

        FROM
        (
        SELECT
        id,
        agreement_number,
        agreement_title,
        agreement_end_date,
        settlement_method,
        agreement_owned_customer,
        service_charge_type,
        service_charge,
        agreement_type,
        agreement_start_date,
        states,
        last_modified_date,
        client_id,
        reason_termination_agreement,
        appendix_id,
        remark,
        created_date,
        agreement_termination_date
        FROM
        hr_protocol
        WHERE
        is_delete = 0
        and client_id=#{param1.id}
        and use_status=1

        <if test="param1.customerTypeList!=null and  param1.customerTypeList.size() > 0">
            and states in
            <foreach collection="param1.customerTypeList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>



        <if test="param1.agreementStartDate!=null ">
            AND agreement_start_date <![CDATA[ >= ]]> #{param1.agreementStartDate}
        </if>
        <if test="param1.agreementStartDateend!=null ">
            AND agreement_start_date <![CDATA[ <= ]]> #{param1.agreementStartDateend}
        </if>
        <if test="param1.agreementEndDate!=null ">
            AND agreement_end_date <![CDATA[ >= ]]> #{param1.agreementEndDate}
        </if>
        <if test="param1.agreementEndDateend!=null ">
            AND agreement_end_date <![CDATA[ <= ]]> #{param1.agreementEndDateend}
        </if>
        order by created_date DESC
        limit 10000000
        )A
        group by A.client_id
    </select>

    <select id="getprotocoendtime" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT *
        FROM hr_protocol
        WHERE client_id = #{id}
          AND is_delete = 0
            and use_status=1
        ORDER BY agreement_start_date DESC LIMIT 1
    </select>

    <select id="getselectHrClients" resultType="cn.casair.dto.HrClientDTO">
        select *
        from hr_client
        where
          AND is_delete = 0
          AND `status` = 0
    </select>

    <select id="IpageGetCustomerType" resultType="cn.casair.dto.HrClientDTO">
        SELECT *
        FROM hr_client
        WHERE is_delete = 0
          AND STATUS = 0
          and id = #{id}
    </select>

    <select id="getselectprotocoendtime" resultType="cn.casair.dto.HrProtocolDTO">
        select *
        from hr_protocol
        where is_delete = 0
          AND states = 0
    </select>

    <select id="getselectclieanId" resultType="java.lang.String">
        SELECT id
        FROM hr_client
        where user_id = #{id}
          AND is_delete = 0
    </select>

    <select id="getselectProtocol" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT *
        FROM hr_protocol
        WHERE client_id = #{clieanId}
          AND states <![CDATA[ < ]]> 2
          AND is_delete = 0
    </select>

    <select id="selectClientid" resultType="java.lang.String">
        SELECT id
        from hr_client
        WHERE user_id = #{userId}
          AND is_delete = 0
    </select>

    <select id="getroleId" resultType="java.lang.Integer">
        SELECT id
        FROM `sys_role`
        WHERE role_key = "client";
    </select>


    <select id="getIdNumber" resultType="cn.casair.dto.HrClientDTO">
        select unit_number as superiorUnitNumber, client_name as superiorUnit
        from hr_client
        where id = #{parid}
          AND is_delete = 0

    </select>
    <select id="getclientunitNumber" resultType="cn.casair.dto.HrClientDTO">
        SELECT hc.*,
               su.user_name  AS userName,
               sus.real_name AS specialized
        FROM hr_client hc
                 LEFT JOIN sys_user su ON su.id = hc.user_id
                 LEFT JOIN sys_user sus ON sus.id = hc.specialized_id
        WHERE hc.id = #{id}
          AND hc.is_delete = 0

    </select>

    <select id="selectCountparid" resultType="cn.casair.dto.HrClientDTO">
        SELECT *
        from hr_client
        where unit_number = #{parid}
          and is_delete = 0

    </select>

    <select id="getSocialSecuritysum" resultType="cn.casair.dto.HrPlatformAccountDTO">
        SELECT *
        FROM `hr_platform_account`
        where account_number = #{socialSecurityAccount}
          and platform_type = 1
          and `status` = 0
          and is_delete = 0
    </select>

    <select id="getCodeKey" resultType="String">
        SELECT item_value
        FROM `code_table`
        where item_name = #{industry}
          and item_status = 0
          and is_delete = 0
    </select>

    <select id="getMedicalInsurance" resultType="cn.casair.dto.HrPlatformAccountDTO">
        SELECT *
        FROM `hr_platform_account`
        where account_number = #{medicalInsuranceAccount}
          and platform_type = 2
          and `status` = 0
          and is_delete = 0
    </select>

    <select id="getProvidentFund" resultType="cn.casair.dto.HrPlatformAccountDTO">
        SELECT *
        FROM `hr_platform_account`
        where account_number = #{providentFundAccount}
          and platform_type = 3
          and `status` = 0
          and is_delete = 0
    </select>

    <select id="getPayroll" resultType="cn.casair.dto.HrPlatformAccountDTO">
        SELECT *
        FROM `hr_platform_account`
        where account_number = #{payrollAccount}
          and platform_type = 4
          and `status` = 0
          and is_delete = 0
    </select>

    <select id="getSocialSecurity" resultType="cn.casair.dto.HrSocialSecurityDTO">
        SELECT *
        FROM `hr_social_security`
        WHERE social_security_name = #{socialSecurityType}
          AND is_delete = 0
    </select>

    <select id="getProvidentFundType" resultType="cn.casair.dto.HrAccumulationFundDTO">
        SELECT *
        FROM `hr_accumulation_fund`
        WHERE type_name = #{providentFundType}
          AND is_delete = 0  LIMIT 1
    </select>

    <select id="getSpecialized" resultType="java.lang.String">
        SELECT su.id
        FROM sys_user su
                 LEFT JOIN sys_user_role sur ON su.id = sur.user_id
                 LEFT JOIN sys_role sr ON sr.id = sur.role_id
        WHERE sr.role_key = "customer_service_staff"
          AND su.real_name = #{specialized}
          AND su.user_status = 1
          AND su.is_delete = 0
        GROUP BY su.id
    </select>


    <select id="getClientUsername" resultType="java.lang.String">
        SELECT su.user_name
        FROM hr_client hc
                 LEFT JOIN   sys_user su on hc.user_id=su.id
        WHERE
            hc.id = #{id}
          AND su.is_delete = 0
         GROUP BY su.id
    </select>

    <select id="selectunitnumber" resultType="cn.casair.domain.HrClient">
        SELECT *
        from hr_client
        where unit_number = #{unitNumber}
          and is_delete = 0

    </select>

    <select id="selectprotocoldate" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT
        *
        FROM
        hr_protocol
        WHERE
        client_id = #{param1.id}
        AND is_delete = 0
        <if test="param1.agreementStartDate!=null ">
            AND agreement_start_date <![CDATA[ >= ]]> #{param1.agreementStartDate}
        </if>
        <if test="param1.agreementStartDateend!=null ">
            AND agreement_start_date <![CDATA[ <= ]]> #{param1.agreementStartDateend}
        </if>
        <if test="param1.agreementEndDate!=null ">
            AND agreement_end_date <![CDATA[ >= ]]> #{param1.agreementEndDate}
        </if>
        <if test="param1.agreementEndDateend!=null ">
            AND agreement_end_date <![CDATA[ <= ]]> #{param1.agreementEndDateend}
        </if>
        ORDER BY agreement_start_date DESC LIMIT 1
    </select>
    <select id="selectByIdUser" resultType="java.lang.String">
        select user_id
        from hr_client
        where id = #{id}
    </select>

    <update id="deleteUser">
        UPDATE sys_user
        set `is_delete`=1,
            user_status=0
        where id = #{id}
    </update>

    <update id="deleteTalent">
        UPDATE hr_talent_staff
        set `is_delete`=1,
            status=1
        where client_id = #{id}
    </update>

    <select id="selectUserId" resultMap="BaseResultMap">
        SELECT *
        from hr_client
        where user_id = #{userId}
          and is_delete = 0

    </select>

    <select id="selectClientID" resultType="cn.casair.dto.HrClientDTO">
            SELECT t3.id as id, t3.client_name as clientName, t3.parent_id as parentId
            FROM (SELECT t1.*,
                         IF(FIND_IN_SET(parent_id, @pids) > 0, @pids := CONCAT(@pids, ',', id), '0') AS ischild
                  FROM (SELECT t.id, t.parent_id, t.client_name,t.is_delete
                        FROM hr_client AS t
                        ORDER BY t.id ASC) t1,
                       (SELECT @pids :=#{id} ) t2  ) t3
            WHERE ischild != '0'    and t3.is_delete=0
    </select>

    <select id="selectClient" resultType="cn.casair.dto.HrClientDTO">
        select id as id, client_name as clientName, parent_id as parentId
        from hr_client
        where is_delete = 0
    </select>
    <select id="selectunitnumberID" resultType="java.lang.String">
        SELECT id
        from hr_client
        where unit_number = #{unitNumber}
          and is_delete = 0
          and status = 0
    </select>
    <select id="selectclientId" resultType="java.lang.String">
        SELECT t3.id as id
        FROM (SELECT t1.*,
                     IF(FIND_IN_SET(parent_id, @pids) > 0, @pids := CONCAT(@pids, ',', id), '0') AS ischild
              FROM (SELECT t.id, t.parent_id, t.client_name,t.is_delete
                    FROM hr_client AS t
                    ORDER BY t.id ASC) t1,
                   (SELECT @pids :=#{id}) t2) t3
        WHERE ischild != '0' and t3.is_delete=0
    </select>
    <select id="getproID" resultType="java.lang.String">
        SELECT
            *
        FROM
            hr_protocol
        WHERE
            client_id = #{id} and states!=3
        AND is_delete = 0
    </select>
    <select id="selectCis" resultType="cn.casair.domain.HrProtocol">
        SELECT
            *
        FROM
            hr_protocol
        WHERE
            client_id =#{clientId}
          AND is_delete = 0 ORDER BY created_date DESC LIMIT 1
    </select>

    <select id="getSelectClientId" resultType="java.lang.String">
        SELECT t3.id
        FROM (
                 SELECT t1.*,
                        IF
                            (FIND_IN_SET(id, @ids) > 0, @ids := CONCAT( parent_id, ',', @ids ), '0') AS isparent
                 FROM (SELECT * FROM hr_client AS t ORDER BY t.id DESC) t1,
                      (SELECT @ids := #{id}) t2
                 ) t3
        WHERE t3.isparent != '0'
	AND t3.is_delete = 0
    </select>

    <select id="getcleanName" resultType="java.lang.String">
        SELECT client_name
        FROM hr_client
        where id = #{agreementOwnedCustomer}
          and is_delete = 0
    </select>

    <select id="selectprotocolId" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT * from hr_protocol where client_id=#{id} and is_delete=0 and type=1
    </select>

    <select id="selectprotocolIds" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT * from hr_protocol where client_id=#{id} and is_delete=0
    </select>

    <select id="selectprotocolDTOS" resultType="java.lang.String">
        SELECT id from hr_protocol where client_id in (SELECT client_id from hr_protocol GROUP BY client_id having count(client_id)>1) and is_delete=0
    </select>
    <select id="getclientName" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM hr_client
        WHERE client_name = #{clientName}
          and is_delete = 0
    </select>
    <select id="selectprotocols" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT * from hr_protocol where  id=#{protocolId} and is_delete=0

    </select>

    <select id="getHrPlatformAccountAccount" resultType="cn.casair.dto.HrPlatformAccountDTO">
        SELECT * from hr_platform_account
        where is_delete = 0
        <if test="param1.accountNumber!=null and param1.accountNumber!=''">
            and  account_number like concat('%',#{param1.accountNumber},'%')
        </if>
        <if test="param1.id != null and param1.id != ''">
            and platform_type = #{param1.id}
        </if>
        <if test="param1.platformType != null and param1.platformType != ''">
            and platform_type = #{param1.platformType}
        </if>
    </select>

    <select id="getOwnedCustomerListselectclientse" resultType="cn.casair.dto.HrProtocolDTO">
        SELECT
        *
        FROM
        hr_client hc
        LEFT JOIN (
        SELECT
        any_value ( id ) protocolId,
        any_value ( client_id ) client_id,
        any_value ( agreement_end_date ) agreement_end_date,
        any_value ( agreement_start_date ) agreement_start_date,
        max( agreement_start_date )
        FROM
        hr_protocol
        WHERE
        is_delete = 0
        and use_status=1
        GROUP BY
        client_id
        ) AS a ON a.client_id = hc.id
        WHERE
        hc.is_delete = 0

            AND hc.id =#{id}


    </select>
    <select id="selectclientsums" resultType="java.lang.Integer">
        SELECT COUNT(id) as peoplesum
        from hr_talent_staff
        WHERE client_id  in
        <foreach collection="clientIdLista" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
          AND is_delete = 0
    </select>


    <update id="updateStatusClient">
        UPDATE hr_client SET status = 0  WHERE id = #{id} AND is_delete=0
    </update>

    <update id="updateClientState">
        UPDATE hr_client SET status = #{status} AND last_modified_date = now() WHERE is_delete = 0 AND id IN
        <foreach collection="clientIds" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </update>

    <update id="updateUserStatusBatch">
        UPDATE
            sys_user su
        LEFT JOIN hr_client hc ON su.id = hc.user_id
        SET su.user_status = #{status} AND su.last_modified_date = now()
        WHERE su.is_delete = 0 AND hc.is_delete = 0 AND hc.id IN
        <foreach collection="clientIds" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </update>

    <select id="findList" resultType="cn.casair.dto.HrClientDTO">
        SELECT hc.*,
               su.user_name  AS userName,
               sus.real_name AS specialized
        FROM hr_client hc
                 LEFT JOIN sys_user su ON su.id = hc.user_id
                 LEFT JOIN sys_user sus ON sus.id = hc.specialized_id
        WHERE hc.is_delete = 0
    </select>

    <select id="findClientById" resultType="cn.casair.dto.HrClientDTO">
        SELECT
            hc.id,
            hc.client_name,
            IF(hc.parent_id = 0,'一级','多级') levelClient,
            a.agreement_title,a.agreement_number
        FROM
            hr_client hc
        LEFT JOIN (
            SELECT
                any_value (id) protocolId,
                any_value (client_id) client_id,
                any_value (agreement_number) agreement_number,
                any_value (agreement_title) agreement_title
            FROM
                hr_protocol
            WHERE
                is_delete = 0
            AND use_status = 1
            GROUP BY
                client_id
        ) AS a ON a.client_id = hc.id
        WHERE
            hc.is_delete = 0 AND hc.id = #{clientId}
    </select>

    <select id="findHrClientBatch" resultType="cn.casair.dto.HrClientDTO">
        SELECT
            hc.id,
            hc.parent_id,
            hc.client_name,
            hc.unit_number,
            hc2.client_name AS superiorUnit
        FROM
            hr_client hc
        LEFT JOIN hr_client hc2 ON hc.parent_id = hc2.id AND hc2.is_delete = 0
        WHERE hc.is_delete = 0 AND hc.id IN
        <foreach collection="clientIdList" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
        ORDER BY hc.created_date
    </select>

    <select id="nonFindPage" resultType="cn.casair.dto.HrClientDTO">
        SELECT
            any_value (hc.id) id,
            any_value (hc.parent_id) parentId,
            any_value (hc.unit_number) unit_number,
            any_value (hc.client_name) client_name,
            any_value (a.agreementNumber) agreementNumber,
            any_value (a.agreementType) agreementType,
            COUNT(hts.id) peoplesum
        FROM
            hr_client hc
        LEFT JOIN (
            SELECT
                any_value (id) protocolId,
                any_value (client_id) client_id,
                any_value (agreement_end_date) agreement_end_date,
                any_value (agreement_start_date) agreement_start_date,
                any_value (agreement_type) agreementType,
                any_value (agreement_number) agreementNumber,
                max(agreement_start_date)
            FROM
                hr_protocol
            WHERE is_delete = 0 AND use_status = 1
            GROUP BY client_id
        ) AS a ON a.client_id = hc.id
        LEFT JOIN hr_talent_staff hts ON hc.id = hts.client_id AND hts.is_delete = 0 AND hts.iz_default = 0
        WHERE hc.is_delete = 0
        <if test="param.idList != null and param.idList.size() > 0">
            AND hc.id IN
            <foreach collection="param.idList" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param.neIds != null and param.neIds.size() > 0">
            AND hc.id NOT IN
            <foreach collection="param.neIds" open="(" separator="," close=")" item="val">
                #{val}
            </foreach>
        </if>
        <if test="param.agreementType != null">
            AND a.agreementType = #{param.agreementType}
        </if>
        GROUP BY hc.id
    </select>

    <select id="findInfo" resultType="cn.casair.dto.HrClientDTO">
        SELECT
            haf.id AS provident_fund_type_id,
            hss.id AS social_security_type_id,
            hc.*,
            hpa.Issuing_bank AS issuingBank
        FROM
            hr_client hc
        LEFT JOIN hr_platform_account hpa ON hc.payroll_account_id = hpa.id
        LEFT JOIN hr_accumulation_fund haf ON hc.provident_fund_type_id = haf.id AND haf.is_delete = 0
        LEFT JOIN hr_social_security hss ON hc.social_security_type_id = hss.id AND hss.is_delete = 0
        WHERE hc.is_delete = 0
        <if test="id != null and id != ''">
            AND hc.id = #{id}
        </if>
    </select>

    <select id="selectFundAndSocial" resultType="cn.casair.dto.HrClientDTO">
        SELECT
            hc.*
        FROM
            hr_client hc
        LEFT JOIN hr_social_security hss ON hc.social_security_type_id = hss.id AND hss.is_delete = 0
        LEFT JOIN hr_accumulation_fund haf ON hc.provident_fund_type_id = haf.id AND haf.is_delete = 0
        WHERE hc.is_delete = 0 AND hc.id IN
        <foreach collection="clientIds" open="(" separator="," close=")" item="val">
            #{val}
        </foreach>
    </select>

    <select id="getParentTree" resultType="cn.casair.dto.HrClientDTO">
        WITH RECURSIVE t1 AS (
            SELECT c1.id, c1.client_name, c1.parent_id,c1.specialized_id
            FROM hr_client c1
            WHERE id IN
                <foreach collection="clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            UNION
            SELECT c2.id, c2.client_name, c2.parent_id,c2.specialized_id
            FROM hr_client c2, t1
            WHERE c2.id = t1.parent_id
        )
        SELECT *FROM t1
    </select>
</mapper>


