package cn.casair.web.rest;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.common.FilterField;
import cn.casair.common.errors.CommonException;
import cn.casair.common.errors.NotFoundException;
import cn.casair.common.utils.excel.ExcelUtils;
import cn.casair.dto.*;
import cn.casair.service.HrBillInvoiceService;
import cn.casair.service.HrBillService;
import cn.casair.service.HrBillTotalService;
import cn.casair.web.rest.util.HeaderUtil;
import cn.casair.web.rest.util.ResponseUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;


/**
 * 开票申请资源
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class HrBillInvoiceResource {


    private final HrBillInvoiceService hrBillInvoiceService;
    private final HrBillService hrBillService;
    private final HrBillTotalService hrBillTotalService;

    public HrBillInvoiceResource(HrBillInvoiceService hrBillInvoiceService, HrBillService hrBillService, HrBillTotalService hrBillTotalService) {
        this.hrBillInvoiceService = hrBillInvoiceService;
        this.hrBillService = hrBillService;
        this.hrBillTotalService = hrBillTotalService;
    }

    /**
     * POST /hr-bill-invoices
     * <p>
     * 创建开票申请
     *
     * @param hrBillInvoiceDTO
     * @return
     */
    @PostMapping("/hr-bill-invoices")
    public ResponseEntity<?> createHrBillInvoice(@RequestBody @Valid HrBillInvoiceDTO hrBillInvoiceDTO) {
        log.info("REST request to save HrBillInvoice:{}", hrBillInvoiceDTO);

        if (hrBillInvoiceDTO.getId() != null) {
            throw new CommonException("hrBillInvoice", "添加时不能设置Id");
        }
        HrBillInvoiceDTO newHrBillInvoice = this.hrBillInvoiceService.insertHrBillInvoice(hrBillInvoiceDTO);
        return ResponseUtil.buildSuccess(newHrBillInvoice);
    }

    /**
     * PUT /hr-bill-invoices
     * <p>
     * 更新开票申请
     *
     * @param hrBillInvoiceDTO
     * @return
     */
    @PutMapping("/hr-bill-invoices")
    public ResponseEntity<?> updateHrBillInvoice(@RequestBody @Valid HrBillInvoiceDTO hrBillInvoiceDTO) {
        log.info("REST request to update HrBillInvoice:{}", hrBillInvoiceDTO);

        return this.hrBillInvoiceService.updateHrBillInvoice(hrBillInvoiceDTO)
            .map(ResponseUtil::buildSuccess)
            .orElseThrow(() -> new NotFoundException("开票申请不存在"));
    }

    /**
     * GET /hr-bill-invoices/:id
     * <p>
     * 查询开票申请详情
     *
     * @param id
     * @return
     */
    @GetMapping("/hr-bill-invoices/{id}")
    public ResponseEntity<?> getHrBillInvoice(@PathVariable String id) {
        log.info("REST request to get HrBillInvoice:{}", id);

        return ResponseUtil.wrapOrNotFound(this.hrBillInvoiceService.getHrBillInvoice(id));
    }

    /**
     * DELETE /hr-bill-invoices/:id
     * <p>
     * 删除开票申请
     *
     * @param id
     * @return
     */
    @DeleteMapping("/hr-bill-invoices/{id}")
    public ResponseEntity<?> deleteHrBillInvoice(@PathVariable String id) {
        log.info("REST request to delete HrBillInvoice:{}", id);
        this.hrBillInvoiceService.deleteHrBillInvoice(id);
        return ResponseUtil.buildSuccess(HeaderUtil.createAlert("hrBillInvoice.deleted", id.toString()));
    }

    /**
     * POST /hr-bill-invoices/deletes
     * <p>
     * 批量删除开票申请
     *
     * @param ids
     * @return
     */
    @PostMapping("/hr-bill-invoices/deletes")
    public ResponseEntity<?> deleteHrBillInvoice(@RequestBody List<String> ids) {
        log.info("Request to delete HrBillInvoices:{}", ids);
        this.hrBillInvoiceService.deleteHrBillInvoice(ids);
        return ResponseUtil.buildSuccess((HeaderUtil.createAlert("hrBillInvoice.deleted", StringUtils.join(ids.toArray(), ","))));
    }

    /**
     * POST /hr-bill-invoices/page
     * <p>
     * 分页查询开票申请
     *
     * @param hrBillInvoiceDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @PostMapping(value = "/hr-bill-invoices/page", params = {"pageNumber", "pageSize"})
    public ResponseEntity<?> findPage(@RequestBody HrBillInvoiceDTO hrBillInvoiceDTO, Long pageNumber, Long pageSize) {
        log.info("Request to find HrBillInvoices:{}, pageNumber: {}, pageSize: {}", hrBillInvoiceDTO, pageNumber, pageSize);

        IPage<HrBillInvoiceDTO> page = this.hrBillInvoiceService.findPage(hrBillInvoiceDTO, pageNumber, pageSize);
        return ResponseUtil.buildSuccess(page);
    }

    /**
     * 根据客户id查询该客户的所有账单，按时间倒序，分页---已弃用
     *
     * @param pageNumber
     * @param pageSize
     * @param clientId
     * @return
     */
    @PostMapping(value = "/hr-bill-invoices/hr-bills/{clientId}")
    public ResponseEntity getBillPage(Long pageNumber, Long pageSize, @PathVariable("clientId") String clientId) {
        log.info("根据客户id查询该客户的所有账单，按时间倒序，分页");
        IPage<HrBillTotalDTO> page = this.hrBillTotalService.getBillTotalPageByClient(clientId, pageNumber, pageSize);
        return ResponseUtil.buildSuccess(page);
    }

    @GetMapping("/hr-bill-invoices/record-template")
    public void downloadTemplate(HttpServletResponse response) {
        log.info("下载开票明细导入模板");
        List<HrBillInvoiceRecordImportDTO> list = new ArrayList<>();
        ExcelUtils.exportExcelWithNoHeader(list, "开票明细导入模板", "sheet1", HrBillInvoiceRecordImportDTO.class, response);
    }

    @PostMapping("/hr-bill-invoices/record-template/import")
    public ResponseEntity importTemplate(@RequestParam("file") MultipartFile file) {
        log.info("导入开票明细");
        ExcelImportResult<HrBillInvoiceRecordImportDTO> result = ExcelUtils.importExcel(file, true, HrBillInvoiceRecordImportDTO.class);

        return ResponseUtil.buildSuccess(result.getList());
    }

    @PostMapping("/hr-bill-invoices/approve")
    public ResponseEntity approve(@RequestBody HrBillInvoiceApproveDTO hrBillInvoiceApproveDTO) {
        log.info("审批");
        hrBillInvoiceService.approve(hrBillInvoiceApproveDTO);

        return ResponseUtil.buildSuccess();
    }

    @PostMapping("/hr-bill-invoices/kp")
    public ResponseEntity kp(@RequestBody HrBillInvoiceKpDTO billInvoiceKpDTO) {
        log.info("开票");
        return ResponseUtil.buildSuccess(hrBillInvoiceService.kp(billInvoiceKpDTO));
    }

    /**
     * 生成会计凭证
     *
     * @param id
     * @return
     */
    @PutMapping("/hr-bill-invoices/confirm/{id}")
    public ResponseEntity kjConfirm(@PathVariable String id) {
        log.info("会计确认-生成凭证");
        hrBillInvoiceService.kjConfirm(id);
        return ResponseUtil.buildSuccess();
    }

    /**
     * 查看开票申请的通知目标角色
     *
     * @return
     */
    @FilterField(includes = "id,roleName")
    @GetMapping("/hr-bill-invoices/notice-roles")
    public ResponseEntity noticeRoles() {
        log.info("查看开票申请的通知目标角色");
        List<RoleDTO> roles = this.hrBillInvoiceService.getNoticeRoles();
        return ResponseUtil.buildSuccess(roles);
    }

    /**
     * 下载开票申请单
     *
     * @param ids
     * @return
     */
    @PostMapping("/hr-bill-invoices/download")
    public String downloadInvoiceApprove(@RequestBody List<String> ids) {
        log.info("Download Invoicing requisition:{}", ids);

        return hrBillInvoiceService.downloadInvoiceApprove(ids);
    }

    /**
     * 创建开票记录
     *
     * @param hrBillInvoiceDTO
     * @return
     */
    @PostMapping("/hr-bill-invoices-save")
    public ResponseEntity<?> saveHrBillInvoice(@RequestBody @Valid HrBillInvoiceDTO hrBillInvoiceDTO) {
        log.info("创建开票记录:{}", hrBillInvoiceDTO);

        HrBillInvoiceDTO newHrBillInvoice = this.hrBillInvoiceService.saveHrBillInvoice(hrBillInvoiceDTO);
        return ResponseUtil.buildSuccess(newHrBillInvoice);
    }

    /**
     * 生成外包客户开票信息
     * @param clientId
     * @return
     */
    @GetMapping("/hr-bill-invoices/generate/{clientId}")
    public ResponseEntity<?> generateHrBillInvoice(@PathVariable String clientId) {
        log.info("生成外包客户开票信息:{}", clientId);

        HrBillInvoiceDTO hrBillInvoiceDTO = this.hrBillInvoiceService.generateHrBillInvoice(clientId);
        return ResponseUtil.buildSuccess(hrBillInvoiceDTO);
    }


    /**
     *  生成凭证
     *
     * @param hrBillInvoiceDTO
     * @return
     */
    @PostMapping(value = "/hr-bill-invoices/create-voucher")
    public ResponseEntity<?> createVoucher(@RequestBody HrBillInvoiceDTO hrBillInvoiceDTO) {
        log.info("开票 生成凭证 HrBillInvoices:{}", hrBillInvoiceDTO);
//        this.hrBillInvoiceService.createVoucher(hrBillInvoiceDTO);
        this.hrBillInvoiceService.createVoucherNcc(hrBillInvoiceDTO);
        return ResponseUtil.buildSuccess();
    }


    /**
     *  作废凭证
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/hr-bill-invoices/delete-voucher")
    public ResponseEntity<?> deleteVoucher(@RequestParam String id) {
        log.info("开票 作废凭证 id:{}", id);
        this.hrBillInvoiceService.deleteVoucher(id);
        return ResponseUtil.buildSuccess();
    }


}

