package cn.casair.common.enums;

/**
 *  报销审批状态枚举类
 * <AUTHOR>
 * @date 2021/11/18 09:20
 */
public enum BillReimbApproveEnums {

    /**
     * 未发起
     */
    NOT_LAUNCH(0, "未发起", ""),

    ON_CUSTOMER_MANAGER(1, "待客服经理审批", UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey()),

    ON_GENERAL_MANAGER(2, "待总经理审批", ""),//弃用

    ON_FINANCIAL_DIRECTOR(3, "待财务负责人审批", ""),//弃用

    ON_CEO(4, "待总裁审批", ""),//弃用

    ON_SUPERVISORS(5, "待监事会主席审批", ""),//弃用

    ON_CHAIRMAN(6, "待董事长审批", ""),//弃用

    ON_ACCOUNTING(7, "待会计确认", UserRoleTypeEnum.ACCOUNTING.getKey()),

    SUCCESS(8, "审批通过", ""),

    REJECT(9, "审核失败", ""),

    ALREADY_CANCEL(10, "已作废", "")

        ;

    private Integer key;

    private String name;

    private String roleKey;

    BillReimbApproveEnums(Integer key, String name, String roleKey) {
        this.key = key;
        this.name = name;
        this.roleKey = roleKey;
    }

    public Integer getKey() {
        return key;
    }

    public String getName() {
        return name;
    }

    public String getRoleKey() {
        return roleKey;
    }

    /**
     * 报销明细类型
     */
    public enum InvoiceTypeEnum {

        DF_SALARY(1, "代发工资"),

        DF_SOCIAL_SECURITY(2, "代缴社保"),

        DF_ACCUMULATION_FOUND(3, "代缴公积金"),

        DF_OTHER(4, "其他"),

        DF_MEDICAL_INSURANCE(5, "代缴医保"),

        SPECIAL_CLIENT(6,"特殊客户"),
        ;

        private final Integer key;

        private final String value;

        public Integer getKey() {
            return key;
        }

        public String getValue() {
            return value;
        }

        InvoiceTypeEnum(Integer key, String value) {
            this.key = key;
            this.value = value;
        }

        public static String getValueByKey(Integer key) {
            for (BillReimbApproveEnums.InvoiceTypeEnum state : values()) {
                if (state.getKey().equals(key)) {
                    return state.getValue();
                }
            }
            return null;
        }



    }
}

