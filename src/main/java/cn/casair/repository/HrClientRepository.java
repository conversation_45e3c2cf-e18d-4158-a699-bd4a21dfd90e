package cn.casair.repository;

import cn.casair.domain.HrClient;
import cn.casair.domain.HrProtocol;
import cn.casair.domain.User;
import cn.casair.domain.UserRole;
import cn.casair.dto.*;
import cn.casair.dto.report.ClientInfoChangeDetailDTO;
import cn.casair.dto.report.ReportQueryParam;
import cn.casair.dto.report.TaxSummaryClientDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 客户组织架构表数据库操作类
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Repository
public interface HrClientRepository extends BaseMapper<HrClient> {


    /**
     * 个税分类汇总-企业
     *
     * @param reportQueryParam
     * @return
     */
    List<TaxSummaryClientDTO> selectTaxSummaryClient(@Param("params") ReportQueryParam reportQueryParam);

    /**
     * 根据条件查询客户列表
     *
     * @param reportQueryParam
     * @return
     */
    List<ClientInfoChangeDetailDTO> selectReducedClient(@Param("params") ReportQueryParam reportQueryParam);

    /**
     * 根据条件查询客户列表
     *
     * @param reportQueryParam
     * @return
     */
    List<ClientInfoChangeDetailDTO> selectIncreasedClient(@Param("params") ReportQueryParam reportQueryParam);

    /**
     * 获取顶级客户
     *
     * @param clientId
     * @return
     */
    HrClient getRootParentClient(@Param("clientId") String clientId);

    /**
     * 获取下级所有客户
     * @param clientId
     * @return
     */
    List<HrClientDTO> getSubordinateClient(@Param("clientId") String clientId);

    /**
     * 获取使用此公积金类型的客户列表
     *
     * @param accumulationFundId
     * @return java.util.List<cn.casair.domain.HrClient>
     * <AUTHOR>
     * @date 2021/11/1
     **/
    List<HrClient> getClientByProvidentFundTypeId(String accumulationFundId);

    /**
     * 根据客户编号获取客户信息
     *
     * @param unitNumber 客户编号
     * @return cn.casair.domain.HrClient
     * <AUTHOR>
     * @date 2021/9/12
     **/
    HrClient selectClientUnitNumber(@Param("unitNumber") String unitNumber);

    /**
     * 获取所有客户名称列表
     *
     * @return java.util.List<cn.casair.domain.HrClient>
     * <AUTHOR>
     * @date 2021/9/12
     **/
    List<HrClient> getClientNameList();


    List<HrSocialSecurityDTO> getHrSocialSelectSecurity();

    List<HrAccumulationFundDTO> getgetHrAccumulationSelectSecurity();

    List<HrPlatformAccountDTO> getHrPlatformAccount();

    List<HrClientDTO> getClientSelect();

    IPage<HrClientDTO> page(Page<HrClient> page, @Param("param1") HrClientDTO hrClientDTO);

    int selectclientsum(String id);

    List<UserDTO> getclientsspecialized();

    int getunitNumber(String unitNumber);

    int insertuser(User user);

    String selectspecialized(String specializedId);

    List<HrClientDTO> getClientId(String id);

    /**
     * 获取客户当前使用协议
     * @param id
     * @return
     */
    List<HrProtocolDTO> getprotoco(String id);

    List<HrDockingDTO> getHrDocking(String protocolId);

    int getclientUser(String userName);

    void insertuserrole(UserRole userRole);

    UserDTO getusername(String id);

    String getspecializedphone(String specializedId);

    List<HrTalentStaffDTO> getTalentStaff(String id);


    HrProtocolDTO selectprotocol(@Param("param1") HrClientDTO hrClientDTO);


    HrProtocolDTO getprotocoendtime(String id);


    List<HrClientDTO> getselectHrClients();

    List<HrClientDTO> IpageGetCustomerType(Page<HrClient> page, HrClientDTO hrClientDTO);

    List<HrProtocolDTO> getselectprotocoendtime();


    String getselectclieanId(String id);

    List<HrProtocolDTO> getselectProtocol(String clieanId);


    String selectClientid(String userId);

    Integer getroleId(String client);


    HrClientDTO getclientunitNumber(String id);


    HrClientDTO getIdNumber(String parid);

    HrClientDTO selectCountparid(String parid);

    String getCodeKey(String industry);

    HrPlatformAccountDTO getSocialSecuritysum(String socialSecurityAccount);

    HrPlatformAccountDTO getMedicalInsurance(String medicalInsuranceAccount);

    HrPlatformAccountDTO getProvidentFund(String providentFundAccount);

    HrPlatformAccountDTO getPayroll(String payrollAccount);

    HrSocialSecurityDTO getSocialSecurity(String socialSecurityType);

    HrAccumulationFundDTO getProvidentFundType(String providentFundType);

    String getSpecialized(String specialized);


    String getClientUsername(String id);

    HrClient selectunitnumber(String unitNumber);

    List<HrProtocolDTO> selectprotocoldate(HrProtocolDTO hrProtocolDTO);

    void deleteUser(String id);

    void deleteTalent(String id);

    String selectByIdUser(String id);

    HrClient selectUserId(String userId);

    List<HrClientDTO> selectClientID(String id);

    List<HrClientDTO> selectClient();

    List<HrClientDTO> getselectHrClient(@Param("ids") List<String> list,@Param("clientName") String clientName);


    List<String> selectunitnumberID(String unitNumber);

    List<String> selectclientId(String id);

    List<String> getproID(String clientId);

    List<HrProtocol> selectCis(String clientId);

    List<String> getSelectClientId(String id);

    String getcleanName(String agreementOwnedCustomer);

    List<HrProtocolDTO> selectprotocolId(String id);

    List<HrProtocolDTO> selectprotocolIds(String id);

    List<String> selectprotocolDTOS();

    int getclientName(String clientName);

    HrProtocolDTO selectprotocols(String protocolId);

    List<HrProtocolDTO> getOwnedCustomerListselectclientse(String id);


    /**
     * 修改客户状态为正常
     * @param id 客户ID
     */
    void updateStatusClient(String id);

    List<HrPlatformAccountDTO> getHrPlatformAccountAccount(@Param("param1") HrPlatformAccountDTO hrPlatformAccountDTO);

    int selectclientsums(@Param("clientIdLista") List<String> clientIdLista);

    @Select("SELECT * FROM  hr_client hc   LEFT JOIN sys_user su on hc.user_id=su.id and su.is_delete=0  ${ew.customSqlSegment}")
    IPage<HrClientDTO> selectPages(Page<HrClient> page, @Param(Constants.WRAPPER) QueryWrapper<HrClient> qws);

    /**
     * 导出查询
     *
     * @param hrClientDTO
     * @return
     */
    List<HrClientDTO> findList(HrClientDTO hrClientDTO);

    /**
     * 根据客户ID查询客户信息
     * @param clientId
     * @return
     */
    HrClientDTO findClientById(@Param("clientId") String clientId);

    /**
     * 查询客户
     * @param clientIdList
     * @return
     */
    List<HrClientDTO> findHrClientBatch(@Param("clientIdList")List<String> clientIdList);

    /**
     * 不分页查询客户列表
     * @param hrClientDTO
     * @return
     */
    List<HrClientDTO> nonFindPage(@Param("param") HrClientDTO hrClientDTO);

    List<HrClientDTO> findInfo(String id);

    /**
     * 查询客户薪酬配置参数
     * @param clientIds
     * @return
     */
    List<HrClientDTO> selectFundAndSocial(@Param("clientIds") List<String> clientIds);

    @Select("select c.id from hr_client c where c.parent_id = #{clientId} and c.is_delete = 0")
    List<String> getChildrenIdsById(@Param("clientId")String clientId);

    List<HrClientDTO> getParentTree(@Param("clientIds") List<String> clientIds);

    /**
     * 批量恢复客户状态
     * @param clientIds
     */
    void updateClientState(@Param("clientIds") List<String> clientIds, @Param("status") Integer status);

    /**
     * 修改客户用户登录状态为禁用
     * @param clientIds
     * @param status
     */
    void updateUserStatusBatch(@Param("clientIds") List<String> clientIds, @Param("status") Integer status);

}
