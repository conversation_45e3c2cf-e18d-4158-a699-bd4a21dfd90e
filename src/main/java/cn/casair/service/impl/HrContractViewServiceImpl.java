package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.ContractEnum;
import cn.casair.common.enums.PcMessageContentEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.enums.UserRoleTypeEnum;
import cn.casair.domain.HrContract;
import cn.casair.domain.HrContractView;
import cn.casair.domain.HrMessageRole;
import cn.casair.domain.Role;
import cn.casair.dto.BatchOptDTO;
import cn.casair.dto.HrApplyOpLogsDTO;
import cn.casair.dto.HrContractDTO;
import cn.casair.dto.HrContractViewDTO;
import cn.casair.dto.HrMessageListDTO;
import cn.casair.dto.JWTMiniDTO;
import cn.casair.dto.JWTUserDTO;
import cn.casair.mapper.HrContractViewMapper;
import cn.casair.repository.HrContractRepository;
import cn.casair.repository.HrContractViewRepository;
import cn.casair.repository.HrMessageRoleRepository;
import cn.casair.repository.RoleRepository;
import cn.casair.service.HrAppendixService;
import cn.casair.service.HrApplyOpLogsService;
import cn.casair.service.HrClientService;
import cn.casair.service.HrContractViewService;
import cn.casair.service.HrMessageListService;
import cn.casair.service.HrNotificationUserService;
import cn.casair.service.HrUpcomingService;
import cn.casair.service.SysOperLogService;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同查看下载申请服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrContractViewServiceImpl extends ServiceImpl<HrContractViewRepository, HrContractView> implements HrContractViewService {

    private final HrContractViewRepository hrContractViewRepository;
    private final HrContractViewMapper hrContractViewMapper;
    private final HrContractRepository hrContractRepository;
    private final HrClientService hrClientService;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrNotificationUserService hrNotificationUserService;
    private final HrUpcomingService hrUpcomingService;
    private final HrMessageListService hrMessageListService;
    private final RoleRepository roleRepository;
    private final HrMessageRoleRepository hrMessageRoleRepository;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;

    /**
     * 创建合同查看下载申请
     *
     * @param hrContractViewDTO
     * @return
     */
    @Override
    public HrContractViewDTO createHrContractView(HrContractViewDTO hrContractViewDTO) {
        log.info("Create new HrContractView:{}", hrContractViewDTO);
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        hrContractViewDTO.setClientId(jwtMiniDTO.getClientId());
        hrContractViewDTO.setLastModifiedDate(LocalDateTime.now());
        HrContractView hrContractView = this.hrContractViewMapper.toEntity(hrContractViewDTO);
        this.hrContractViewRepository.insert(hrContractView);
        HrContractViewDTO toDto = this.hrContractViewMapper.toDto(hrContractView);
        hrContractRepository.updateContractViewId(toDto.getId(), toDto.getContractId());
        String message = jwtMiniDTO.getName() + "申请" + ServiceCenterEnum.ContractViewApplyStateEnum.getValueByKey(hrContractViewDTO.getApplyState()) + "合同";
        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(toDto.getId(), null, jwtMiniDTO.getId(), message, null, true, null, ServiceCenterEnum.CONTRACT_VIEW_DOWNLOAD.getKey());
        this.hrNotificationUserService.saveRemindContent(hrContractViewDTO.getClientId(), ServiceCenterEnum.CONTRACT_VIEW_DOWNLOAD.getKey(), ServiceCenterEnum.ContractViewEnum.INITIATE_APPLICATION.getKey(), message, jwtMiniDTO.getId());
        hrUpcomingService.createServiceUpcoming(hrContractViewDTO.getId(), hrContractViewDTO.getStaffId(), "合同查看下载-审核" + hrContractViewDTO.getName() + "的合同" + ServiceCenterEnum.ContractViewApplyStateEnum.getValueByKey(hrContractViewDTO.getApplyState()) + "申请", LocalDate.now(), 0);
        return toDto;
    }

    /**
     * 查询合同查看下载申请详情
     *
     * @param id
     * @return
     */
    @Override
    public HrContractViewDTO getHrContractView(String id) {
        log.info("Get HrContractView :{}", id);

        HrContractViewDTO hrContractViewDTO = this.hrContractViewRepository.findInfoById(id);
        List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(id, null);
        if (CollectionUtils.isNotEmpty(applyOpLogsList)) {
            hrContractViewDTO.setApplyOpLogsList(applyOpLogsList);
        }
        return hrContractViewDTO;
    }

    /**
     * 批量删除合同查看下载申请
     *
     * @param ids
     */
    @Override
    public void deleteHrContractView(List<String> ids) {
        log.info("Delete HrContractViews:{}", ids);

        hrContractRepository.updateContractViewNull(ids);
        this.hrContractViewRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询合同查看下载申请
     *
     * @param hrContractViewDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrContractViewDTO> findPage(HrContractViewDTO hrContractViewDTO, Long pageNumber, Long pageSize) {
        Page<HrContractView> page = new Page<>(pageNumber, pageSize);
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        IPage<HrContractViewDTO> iPage = this.hrContractViewRepository.findPage(page, hrContractViewDTO, clientIds);
        return iPage;
    }

    @Override
    public String export(HrContractViewDTO hrContractViewDTO) {
        List<HrContractViewDTO> list = this.hrContractViewRepository.findList(hrContractViewDTO, hrClientService.selectClientIdByUserId());
        List<String> ids = list.stream().map(HrContractViewDTO::getId).collect(Collectors.toList());
        String fileUrl = this.hrAppendixService.uploadExportFile(list, ModuleTypeEnum.CONTRACT.getValue(), HrContractViewDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.CONTRACT.getValue(), BusinessTypeEnum.EXPORT.getKey(),
            JSON.toJSONString(ids), ids.size(), fileUrl);
        return fileUrl;
    }

    /**
     * 客户批量操作--通过/拒绝
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public Map<String, Object> batchOperationContractView(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> errorStaffList = new ArrayList<>();
        List<HrContractViewDTO> hrContractViewDTOS = hrContractViewRepository.findBatchIds(batchOptDTO.getApplyIdList());
        for (HrContractViewDTO hrContractViewDTO : hrContractViewDTOS) {
            if (!hrContractViewDTO.getStates().equals(ServiceCenterEnum.ContractViewStateEnum.TO_BE_CUSTOMER_REVIEWED.getKey())) {
                errorStaffList.add(hrContractViewDTO.getName());
                continue;
            }
            String message = "";
            if (batchOptDTO.getOpt()) {
                hrContractViewDTO.setStates(ServiceCenterEnum.ContractViewStateEnum.APPROVED.getKey())
                    .setIsAgainApply(ServiceCenterEnum.ContractViewNeedEnum.UNWANTED.getKey())
                    .setAuditDate(LocalDate.now());
                message = jwtUserDTO.getRealName() + "审核通过了" + hrContractViewDTO.getName() + "合同" + ServiceCenterEnum.ContractViewApplyStateEnum.getValueByKey(hrContractViewDTO.getApplyState()) + "申请";
            } else {
                hrContractViewDTO.setStates(ServiceCenterEnum.ContractViewStateEnum.AUDIT_REJECTION.getKey())
                    .setIsAgainApply(ServiceCenterEnum.ContractViewNeedEnum.NEED.getKey())
                    .setAuditDate(LocalDate.now());
                message = jwtUserDTO.getRealName() + "审核拒绝了" + hrContractViewDTO.getName() + "合同" + ServiceCenterEnum.ContractViewApplyStateEnum.getValueByKey(hrContractViewDTO.getApplyState()) + "申请。拒绝理由：" + batchOptDTO.getCheckerReason();
            }
            hrContractViewRepository.updateContractView(hrContractViewDTO);
            hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrContractViewDTO.getId(), null, jwtUserDTO.getId(), message, null, false, null, ServiceCenterEnum.CONTRACT_VIEW_DOWNLOAD.getKey());
            this.hrNotificationUserService.saveRemindContent(hrContractViewDTO.getClientId(), ServiceCenterEnum.CONTRACT_VIEW_DOWNLOAD.getKey(), ServiceCenterEnum.ContractViewEnum.CUSTOMER_REVIEW.getKey(), message, jwtUserDTO.getId());
            hrUpcomingService.updateUpcoming(hrContractViewDTO.getId());
        }
        Map<String, Object> objectMap = new HashMap<>();
        if (errorStaffList.size() == 0) {
            objectMap.put("checkCode", 200);
            objectMap.put("checkMsg", "操作成功！");
        } else {
            objectMap.put("checkCode", 500);
            objectMap.put("checkMsg", String.join(",", errorStaffList) + "操作失败！失败原因：目前操作不需要客户审核");
        }
        return objectMap;
    }

    /**
     * 单个操作--通过/拒绝
     *
     * @param batchOptDTO
     * @return
     */
    @Override
    public HrContractViewDTO specialSupervisorOperation(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        HrContractViewDTO hrContractViewDTO = hrContractViewRepository.findInfoById(batchOptDTO.getApplyId());
        String message = "";
        Integer launchType = null;
        if (batchOptDTO.getOpt()) {
            if (hrContractViewDTO.getStates().equals(ServiceCenterEnum.ContractViewStateEnum.TO_BE_REVIEWED.getKey())) {
                launchType = ServiceCenterEnum.ContractViewEnum.MANAGER_REVIEW.getKey();
                if (batchOptDTO.getIsNeed().equals(ServiceCenterEnum.ContractViewNeedEnum.NEED.getKey())) {
                    hrContractViewDTO.setStates(ServiceCenterEnum.ContractViewStateEnum.TO_BE_CUSTOMER_REVIEWED.getKey())
                        .setIsAgainApply(ServiceCenterEnum.ContractViewNeedEnum.UNWANTED.getKey());
                    Role role = roleRepository.selectOneForKey(UserRoleTypeEnum.CLIENT.getKey());
                    HrMessageListDTO messageListDTO = new HrMessageListDTO()
                        .setContentType(2)
                        .setContent(PcMessageContentEnum.CONTRACT_VIEW_APPLY.getKey())
                        .setTitle("审核" + hrContractViewDTO.getName() + "的合同" + ServiceCenterEnum.ContractViewApplyStateEnum.getValueByKey(hrContractViewDTO.getApplyState()) + "申请")
                        .setCreatedById(hrContractViewDTO.getUserId())
                        .setRecipientRoleIds(String.valueOf(role.getId()))
                        .setNoticeUserIds(Collections.singletonList(hrContractViewDTO.getUserId()));
                    HrMessageListDTO hrMessageList = this.hrMessageListService.createHrMessageList(messageListDTO);
                    HrMessageRole hrMessageRole = new HrMessageRole();
                    hrMessageRole.setMessageId(hrMessageList.getId());
                    hrMessageRole.setUserId(hrContractViewDTO.getUserId());
                    hrMessageRoleRepository.insert(hrMessageRole);
                    hrUpcomingService.createServiceUpcoming(hrContractViewDTO.getId(), hrContractViewDTO.getStaffId(), "合同查看下载-审核" + hrContractViewDTO.getName() + "的合同" + ServiceCenterEnum.ContractViewApplyStateEnum.getValueByKey(hrContractViewDTO.getApplyState()) + "申请", LocalDate.now(), 1);
                } else {
                    hrContractViewDTO.setStates(ServiceCenterEnum.ContractViewStateEnum.APPROVED.getKey())
                        .setIsAgainApply(ServiceCenterEnum.ContractViewNeedEnum.UNWANTED.getKey())
                        .setAuditDate(LocalDate.now());
                    hrUpcomingService.updateUpcoming(hrContractViewDTO.getId());
                }
            } else if (hrContractViewDTO.getStates().equals(ServiceCenterEnum.ContractViewStateEnum.TO_BE_CUSTOMER_REVIEWED.getKey())) {
                launchType = ServiceCenterEnum.ContractViewEnum.MANAGER_REVIEW.getKey();
                hrContractViewDTO.setStates(ServiceCenterEnum.ContractViewStateEnum.APPROVED.getKey())
                    .setIsAgainApply(ServiceCenterEnum.ContractViewNeedEnum.UNWANTED.getKey());
                hrUpcomingService.updateUpcoming(hrContractViewDTO.getId());
            } else {
                launchType = ServiceCenterEnum.ContractViewEnum.MANAGER_REVIEW.getKey();
                hrContractViewDTO.setStates(ServiceCenterEnum.ContractViewStateEnum.APPROVED.getKey())
                    .setIsAgainApply(ServiceCenterEnum.ContractViewNeedEnum.UNWANTED.getKey())
                    .setAuditDate(LocalDate.now());
                hrUpcomingService.updateUpcoming(hrContractViewDTO.getId());
            }
            message = jwtUserDTO.getRealName() + "审核通过了" + hrContractViewDTO.getName() + "的合同" + ServiceCenterEnum.ContractViewApplyStateEnum.getValueByKey(hrContractViewDTO.getApplyState()) + "申请";
        } else {
            hrContractViewDTO.setStates(ServiceCenterEnum.ContractViewStateEnum.AUDIT_REJECTION.getKey())
                .setIsAgainApply(ServiceCenterEnum.ContractViewNeedEnum.NEED.getKey())
                .setAuditDate(LocalDate.now());
            message = jwtUserDTO.getRealName() + "审核拒绝了" + hrContractViewDTO.getName() + "的合同" + ServiceCenterEnum.ContractViewApplyStateEnum.getValueByKey(hrContractViewDTO.getApplyState()) + "申请。拒绝理由：" + batchOptDTO.getCheckerReason();
            hrUpcomingService.updateUpcoming(hrContractViewDTO.getId());
        }
        hrContractViewDTO.setIsNeed(batchOptDTO.getIsNeed());
        hrContractViewRepository.updateContractView(hrContractViewDTO);
        hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrContractViewDTO.getId(), null, jwtUserDTO.getId(), message, null, false, null, ServiceCenterEnum.CONTRACT_VIEW_DOWNLOAD.getKey());
        this.hrNotificationUserService.saveRemindContent(hrContractViewDTO.getClientId(), ServiceCenterEnum.CONTRACT_VIEW_DOWNLOAD.getKey(), launchType, message, jwtUserDTO.getId());
        return hrContractViewDTO;
    }

    /**
     * 每天1点检测合同查看下载状态
     */
    @Override
    public void scheduleTaskToUpdateContractViewState() {
        List<HrContractDTO> hrContractDTOS = hrContractRepository.selectContract(new QueryWrapper<HrContract>()
            .eq("hcv.states", ServiceCenterEnum.ContractViewStateEnum.APPROVED.getKey())
            .isNotNull("contract_view_id")
            .ne("hc.state", ContractEnum.ContractState.NOT_ACTIVE.getKey())
            .eq("hc.is_delete", 0)
            .orderByDesc("hc.contract_start_date"));
        if (CollectionUtils.isNotEmpty(hrContractDTOS)) {
            for (HrContractDTO contractDTO : hrContractDTOS) {
                LocalDate localDate = contractDTO.getAuditDate().plusDays(3);
                if (localDate.isBefore(LocalDate.now())) {
                    HrContractViewDTO hrContractViewDTO = new HrContractViewDTO();
                    hrContractViewDTO.setIsAgainApply(ServiceCenterEnum.ContractViewNeedEnum.NEED.getKey()).setId(contractDTO.getContractViewId());
                    hrContractViewRepository.updateContractView(hrContractViewDTO);
                }
            }
        }
    }
}
