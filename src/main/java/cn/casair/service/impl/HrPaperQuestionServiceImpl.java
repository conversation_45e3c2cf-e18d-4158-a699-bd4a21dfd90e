package cn.casair.service.impl;

import cn.casair.domain.HrPaperQuestion;
import cn.casair.dto.HrPaperQuestionDTO;
import cn.casair.mapper.HrPaperQuestionMapper;
import cn.casair.repository.HrPaperQuestionRepository;
import cn.casair.service.HrPaperQuestionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
/**
 * 试卷题库关联表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrPaperQuestionServiceImpl extends ServiceImpl<HrPaperQuestionRepository, HrPaperQuestion>implements HrPaperQuestionService {

    private static final Logger log=LoggerFactory.getLogger(HrPaperQuestionServiceImpl.class);

    private final HrPaperQuestionRepository hrPaperQuestionRepository;

    private final HrPaperQuestionMapper hrPaperQuestionMapper;

    public HrPaperQuestionServiceImpl(HrPaperQuestionRepository hrPaperQuestionRepository, HrPaperQuestionMapper hrPaperQuestionMapper){
    this.hrPaperQuestionRepository = hrPaperQuestionRepository;
    this.hrPaperQuestionMapper= hrPaperQuestionMapper;
    }

    /**
     * 创建试卷题库关联表
     * @param hrPaperQuestionDTO
     * @return
     */
    @Override
    public HrPaperQuestionDTO createHrPaperQuestion(HrPaperQuestionDTO hrPaperQuestionDTO){
    log.info("Create new HrPaperQuestion:{}", hrPaperQuestionDTO);

    HrPaperQuestion hrPaperQuestion =this.hrPaperQuestionMapper.toEntity(hrPaperQuestionDTO);
    this.hrPaperQuestionRepository.insert(hrPaperQuestion);
    return this.hrPaperQuestionMapper.toDto(hrPaperQuestion);
    }

    /**
     * 修改试卷题库关联表
     * @param hrPaperQuestionDTO
     * @return
     */
    @Override
    public Optional<HrPaperQuestionDTO>updateHrPaperQuestion(HrPaperQuestionDTO hrPaperQuestionDTO){
    return Optional.ofNullable(this.hrPaperQuestionRepository.selectById(hrPaperQuestionDTO.getId()))
    .map(roleTemp->{
    HrPaperQuestion hrPaperQuestion =this.hrPaperQuestionMapper.toEntity(hrPaperQuestionDTO);
    this.hrPaperQuestionRepository.updateById(hrPaperQuestion);
    log.info("Update HrPaperQuestion:{}", hrPaperQuestionDTO);
    return hrPaperQuestionDTO;
    });
    }

    /**
     * 查询试卷题库关联表详情
     * @param id
     * @return
     */
    @Override
    public HrPaperQuestionDTO getHrPaperQuestion(String id){
    log.info("Get HrPaperQuestion :{}",id);

    HrPaperQuestion hrPaperQuestion =this.hrPaperQuestionRepository.selectById(id);
    return this.hrPaperQuestionMapper.toDto(hrPaperQuestion);
    }

    /**
     * 删除试卷题库关联表
     * @param id
     */
    @Override
    public void deleteHrPaperQuestion(String id){
    Optional.ofNullable(this.hrPaperQuestionRepository.selectById(id))
    .ifPresent(hrPaperQuestion ->{
    this.hrPaperQuestionRepository.deleteById(id);
    log.info("Delete HrPaperQuestion:{}", hrPaperQuestion);
    });
    }

    /**
     * 批量删除试卷题库关联表
     * @param ids
     */
    @Override
    public void deleteHrPaperQuestion(List<String>ids){
    log.info("Delete HrPaperQuestions:{}",ids);
    this.hrPaperQuestionRepository.deleteBatchIds(ids);
    }



}
