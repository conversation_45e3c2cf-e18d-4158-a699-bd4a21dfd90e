package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.domain.HrStaffFamily;
import cn.casair.dto.HrStaffFamilyDTO;
import cn.casair.mapper.HrStaffFamilyMapper;
import cn.casair.repository.HrStaffFamilyRepository;
import cn.casair.service.HrStaffFamilyService;
import cn.casair.service.SysOperLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
/**
 * 家庭成员服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HrStaffFamilyServiceImpl extends ServiceImpl<HrStaffFamilyRepository, HrStaffFamily>implements HrStaffFamilyService {

    private static final Logger log=LoggerFactory.getLogger(HrStaffFamilyServiceImpl.class);

    private final HrStaffFamilyRepository hrStaffFamilyRepository;

    private final HrStaffFamilyMapper hrStaffFamilyMapper;

    private final SysOperLogService sysOperLogService;

    public HrStaffFamilyServiceImpl(HrStaffFamilyRepository hrStaffFamilyRepository, HrStaffFamilyMapper hrStaffFamilyMapper, SysOperLogService sysOperLogService){
        this.hrStaffFamilyRepository = hrStaffFamilyRepository;
        this.hrStaffFamilyMapper= hrStaffFamilyMapper;
        this.sysOperLogService = sysOperLogService;
    }

    /**
     * 创建家庭成员
     * @param hrStaffFamilyDTO
     * @return
     */
    @Override
    public List<HrStaffFamilyDTO> createHrStaffFamily(HrStaffFamilyDTO hrStaffFamilyDTO){
        log.info("Create new HrStaffFamily:{}", hrStaffFamilyDTO);

        HrStaffFamily hrStaffFamily =this.hrStaffFamilyMapper.toEntity(hrStaffFamilyDTO);
        this.hrStaffFamilyRepository.insert(hrStaffFamily);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.STAFF_FAMILY.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrStaffFamilyDTO),
            HrStaffFamilyDTO.class,
            null,
            JSON.toJSONString(hrStaffFamily)
        );
        return this.findFamilyList(hrStaffFamilyDTO.getStaffId());
    }

    /**
     * 查询家庭成员
     * @param staffId 员工ID
     * @return
     */
    @Override
    public List<HrStaffFamilyDTO> findFamilyList(String staffId) {
        List<HrStaffFamilyDTO> hrStaffFamilyDTOS = hrStaffFamilyMapper.toDto(
            this.hrStaffFamilyRepository.selectList(new QueryWrapper<HrStaffFamily>().eq("staff_id", staffId).eq("is_delete", 0)));
        return hrStaffFamilyDTOS;
    }

    /**
     * 修改家庭成员
     * @param hrStaffFamilyDTO
     * @return
     */
    @Override
    public Optional<List<HrStaffFamilyDTO>> updateHrStaffFamily(HrStaffFamilyDTO hrStaffFamilyDTO){
        return Optional.ofNullable(this.hrStaffFamilyRepository.selectById(hrStaffFamilyDTO.getId()))
        .map(roleTemp->{
            HrStaffFamily hrStaffFamily =this.hrStaffFamilyMapper.toEntity(hrStaffFamilyDTO);
            this.hrStaffFamilyRepository.updateById(hrStaffFamily);
            log.info("Update HrStaffFamily:{}", hrStaffFamilyDTO);
            // 操作日志
            this.sysOperLogService.insertSysOperLog(
                ModuleTypeEnum.STAFF_FAMILY.getValue(),
                BusinessTypeEnum.UPDATE.getKey(),
                JSON.toJSONString(hrStaffFamilyDTO),
                HrStaffFamilyDTO.class,
                null,
                JSON.toJSONString(roleTemp),
                JSON.toJSONString(hrStaffFamilyDTO),
                null,
                HrStaffFamilyDTO.class
            );
            return this.findFamilyList(hrStaffFamilyDTO.getStaffId());
        });
    }

    /**
     * 查询家庭成员详情
     * @param id
     * @return
     */
    @Override
    public HrStaffFamilyDTO getHrStaffFamily(String id){
        log.info("Get HrStaffFamily :{}",id);

        HrStaffFamily hrStaffFamily =this.hrStaffFamilyRepository.selectById(id);
        return this.hrStaffFamilyMapper.toDto(hrStaffFamily);
    }

    /**
     * 删除家庭成员
     * @param id
     */
    @Override
    public List<HrStaffFamilyDTO> deleteHrStaffFamily(String id){
        HrStaffFamily hrStaffFamily = this.hrStaffFamilyRepository.selectById(id);
        this.hrStaffFamilyRepository.deleteById(id);
        log.info("Delete HrStaffFamily:{}", hrStaffFamily);
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.STAFF_FAMILY.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除" + ModuleTypeEnum.STAFF_FAMILY.getValue()+": "+ hrStaffFamily.getFamilyName(),
            null,
            null,
            null,
            JSON.toJSONString(hrStaffFamily),
            null
        );
        return this.findFamilyList(hrStaffFamily.getStaffId());
    }
}
