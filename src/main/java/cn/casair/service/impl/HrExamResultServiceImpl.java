package cn.casair.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.cache.RedisCache;
import cn.casair.common.enums.RecruitmentBrochure;
import cn.casair.common.enums.RedisKeyEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.excel.HrExamResultImport;
import cn.casair.dto.excel.HrExamResultTemplate;
import cn.casair.dto.excel.HrProfileGradesImport;
import cn.casair.mapper.HrExamResultMapper;
import cn.casair.mapper.HrRegistrationDetailsMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.asynchronous.QuestionExamComponent;
import cn.casair.service.util.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 考试结果表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrExamResultServiceImpl extends ServiceImpl<HrExamResultRepository, HrExamResult> implements HrExamResultService {

    private final HrExamResultRepository hrExamResultRepository;
    private final HrExamResultMapper hrExamResultMapper;
    private final HrRegistrationDetailsEvaluationRepository hrRegistrationDetailsEvaluationRepository;
    private final HrRecruitmentBrochureRepository hrRecruitmentBrochureRepository;
    private final HrPaperManagementService hrPaperManagementService;
    private final HrStationService hrStationService;
    private final QuestionExamComponent questionExamComponent;
    private final HrTalentStaffService hrTalentStaffService;
    private final HrAppendixService hrAppendixService;
    private final CodeTableService codeTableService;
    private final HrExamService hrExamService;
    private final HrExamResultDetailsService hrExamResultDetailsService;
    private final HrExamDetailsRepository hrExamDetailsRepository;
    private final HrClientService hrClientService;
    private final RedisCache redisCache;
    private final HrRecruitmentStationRepository hrRecruitmentStationRepository;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final HrRegistrationDetailsRepository hrRegistrationDetailsRepository;
    private final HrRecruitmentBulletinRepository hrRecruitmentBulletinRepository;
    private final SysOperLogService sysOperLogService;
    private final HrRegistrationDetailsMapper hrRegistrationDetailsMapper;
    private final HrRecruitmentBulletinService hrRecruitmentBulletinService;

    @Lazy
    @Resource
    private HrRecruitmentBrochureService hrRecruitmentBrochureService;

    @Value("${minio.excelPrefix}")
    private String excelPrefix;


    /**
     * 创建考试结果表
     *
     * @param hrExamResultDTO
     * @return
     */
    @Override
    public HrExamResultDTO createHrExamResult(HrExamResultDTO hrExamResultDTO) {
        log.info("Create new HrExamResult:{}", hrExamResultDTO);

        HrExamResult hrExamResult = this.hrExamResultMapper.toEntity(hrExamResultDTO);
        this.hrExamResultRepository.insert(hrExamResult);
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.EXAM_RESULT.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrExamResultDTO),
            HrExamResultDTO.class,
            null,
            JSON.toJSONString(hrExamResultDTO)
        );
        return this.hrExamResultMapper.toDto(hrExamResult);
    }

    /**
     * 修改考试结果表
     *
     * @param hrExamResultDTO
     * @return
     */
    @Override
    public Optional<HrExamResultDTO> updateHrExamResult(HrExamResultDTO hrExamResultDTO) {
        return Optional.ofNullable(this.hrExamResultRepository.selectById(hrExamResultDTO.getId()))
            .map(roleTemp -> {
                HrExamResult hrExamResult = this.hrExamResultMapper.toEntity(hrExamResultDTO);
                this.hrExamResultRepository.updateById(hrExamResult);
                log.info("Update HrExamResult:{}", hrExamResultDTO);
                // 操作日志
                this.sysOperLogService.insertSysOperLog(
                    ModuleTypeEnum.EXAM_RESULT.getValue(),
                    BusinessTypeEnum.UPDATE.getKey(),
                    JSON.toJSONString(hrExamResultDTO),
                    HrExamResultDTO.class,
                    null,
                    JSON.toJSONString(roleTemp),
                    JSON.toJSONString(hrExamResultDTO),
                    null,
                    HrExamResultDTO.class
                );
                return hrExamResultDTO;
            });
    }

    /**
     * 查询考试结果表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrExamResultDTO getHrExamResult(String id) {
        log.info("Get HrExamResult :{}", id);

        HrExamResult hrExamResult = this.hrExamResultRepository.selectById(id);
        return this.hrExamResultMapper.toDto(hrExamResult);
    }

    /**
     * 删除考试结果表
     *
     * @param id
     */
    @Override
    public void deleteHrExamResult(String id) {
        Optional.ofNullable(this.hrExamResultRepository.selectById(id))
            .ifPresent(hrExamResult -> {
                this.hrExamResultRepository.deleteById(id);
                log.info("Delete HrExamResult:{}", hrExamResult);
            });
    }

    /**
     * 批量删除考试结果表
     *
     * @param ids
     */
    @Override
    public void deleteHrExamResult(List<String> ids) {
        log.info("Delete HrExamResults:{}", ids);
        HrExamResult hrExamResult = getById(ids.get(0));
        List<HrExamResult> hrExamResults = hrExamResultRepository.selectBatchIds(ids);
        this.hrExamResultRepository.deleteBatchIds(ids);
        //重新计算
        if (StringUtils.isNotBlank(hrExamResult.getPaperId())) {
            examNumber(hrExamResult.getPaperId(), hrExamResult.getProfessionName(), hrExamResult.getExamName());
        }
        // 操作日志
        //this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.EXAM_RESULT.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids));
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> collect = hrExamResults.stream().map(HrExamResult::getCard).collect(Collectors.toList());
        this.sysOperLogService.insertSysOper(
            ModuleTypeEnum.EXAM_RESULT.getValue(),
            BusinessTypeEnum.DELETE.getKey(),
            "删除考试结果，考生身份证号: " + JSON.toJSONString(collect),
            null,
            null,
            null,
            JSON.toJSONString(hrExamResults),
            jwtUserDTO
        );
    }

    /**
     * 分页查询考试结果表
     *
     * @param hrExamResultDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrExamResultDTO hrExamResultDTO, Long pageNumber, Long pageSize) {
        Page<HrExamResult> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrExamResult> qw = new QueryWrapper<>();
        if (StringUtils.isBlank(hrExamResultDTO.getPaperId())) {
//            throw new CommonException("请选择试卷id");
            return new Page();
        }
        if (StringUtils.isNotBlank(hrExamResultDTO.getIsSave()) && "1".equals(hrExamResultDTO.getIsSave())) {
            QueryWrapper<HrExam> hrExamQueryWrapper = new QueryWrapper<>();
            hrExamQueryWrapper.eq("paper_id", hrExamResultDTO.getPaperId());
            hrExamQueryWrapper.eq("exam_name", hrExamResultDTO.getExamName());
            hrExamQueryWrapper.eq("profession_name", hrExamResultDTO.getProfessionName());
            List<HrExam> list = hrExamService.list(hrExamQueryWrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new CommonException("试卷名称已存在,请重新输入");
            }
        }
        qw.eq("paper_id", hrExamResultDTO.getPaperId());
        qw.eq(StringUtils.isNotBlank(hrExamResultDTO.getProfessionName()), "profession_name", hrExamResultDTO.getProfessionName());
        qw.eq(StringUtils.isNotBlank(hrExamResultDTO.getExamName()), "exam_name", hrExamResultDTO.getExamName());
        qw.like(StringUtils.isNotBlank(hrExamResultDTO.getClientName()), "client_name", hrExamResultDTO.getClientName());
        qw.like(StringUtils.isNotBlank(hrExamResultDTO.getStudentsName()), "students_name", hrExamResultDTO.getStudentsName());
        qw.like(StringUtils.isNotBlank(hrExamResultDTO.getCard()), "card", hrExamResultDTO.getCard());
        qw.like(StringUtils.isNotBlank(hrExamResultDTO.getPhone()), "phone", hrExamResultDTO.getPhone());
        qw.ge(StringUtils.isNotBlank(hrExamResultDTO.getStartExamDate()), "exam_date", hrExamResultDTO.getStartExamDate());
        if (StringUtils.isNotBlank(hrExamResultDTO.getEndExamDate())) {
            String afterDate = DateUtils.selectBeforeDate(hrExamResultDTO.getEndExamDate(), -1);
            qw.lt("exam_date", afterDate);
        }
        //排序
        if (StringUtils.isNotBlank(hrExamResultDTO.getOrder())) {
            if (hrExamResultDTO.getOrder().equals("DESC")) {
                qw.orderBy(StringUtils.isNotBlank(hrExamResultDTO.getField()), false, hrExamResultDTO.getField());
            } else {
                qw.orderBy(StringUtils.isNotBlank(hrExamResultDTO.getField()), true, hrExamResultDTO.getField());
            }

        }


        IPage iPage = this.hrExamResultRepository.selectPage(page, qw);
        List<HrExamResultDTO> list = this.hrExamResultMapper.toDto(iPage.getRecords());
        for (HrExamResultDTO examResultDTO : list) {
            QueryWrapper<HrRegistrationDetailsEvaluation> evaluationQueryWrapper = new QueryWrapper<>();
            evaluationQueryWrapper.eq("details_id", examResultDTO.getId());
            List<HrRegistrationDetailsEvaluation> hrRegistrationDetailsEvaluations = hrRegistrationDetailsEvaluationRepository.selectList(evaluationQueryWrapper);
            examResultDTO.setHrRegistrationEvaluationList(hrRegistrationDetailsEvaluations);
        }
        iPage.setRecords(list);
        return iPage;
    }

    @Override
    public IPage<HrPaperManagementDTO> findExamPage(HrPaperManagementDTO hrPaperManagementDTO, Long pageNumber, Long pageSize) {
        //获取所有试卷（试卷分页接口）
        IPage page = hrPaperManagementService.findPage(hrPaperManagementDTO, pageNumber, pageSize);
        List<HrPaperManagementDTO> hrPaperManagementDTOList = page.getRecords();
        for (HrPaperManagementDTO paperManagementDTO : hrPaperManagementDTOList) {
            QueryWrapper<HrExamResult> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("paper_id", paperManagementDTO.getId());
            //查询试卷的考试人数
            int totalCount = count(queryWrapper);
            //查询试卷的及格人数
            queryWrapper.ge("score", paperManagementDTO.getPassLine());
            int passLineCount = count(queryWrapper);
            paperManagementDTO.setExamsNumber(totalCount);
            //计算通过率
            //计算通过率
            if (totalCount != 0) {
                BigDecimal bigDecimal = new BigDecimal((float) passLineCount / totalCount).setScale(2, BigDecimal.ROUND_HALF_UP);
                //hrExamDTO.setExamsPassingRate(bigDecimal);
            }

        }
        return page;
    }

    /**
     * 获取所有试卷名称
     *
     * @return
     */
    @Override
    public HashMap<String, String> getPager() {
        QueryWrapper<HrPaperManagement> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("any_value(id) id ,paper_name ");
        queryWrapper.eq("is_preset", 0);
        queryWrapper.groupBy("paper_name");
        List<HrPaperManagement> list = hrPaperManagementService.list(queryWrapper);
        HashMap<String, String> hashMap = new HashMap<>();
        for (HrPaperManagement hrPaperManagement : list) {
            hashMap.put(hrPaperManagement.getPaperName(), hrPaperManagement.getId());
        }
        return hashMap;
    }

    /**
     * 考试结果导入
     *
     * @param file
     * @return
     */
    @Override
    public String importExamResult(MultipartFile file, String paperId, String professionName, String examName) {
        // 设置进度条
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.EXAM_RESULT.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.questionExamComponent.examResultImport(inputStream, redisKey, jwtUserDTO, paperId, professionName, examName);
        return redisKey;
//        //判断导入试卷是否存在
//        HrPaperManagement hrPaperManagement = hrPaperManagementService.getById(paperId);
//        if (hrPaperManagement == null) {
//            throw new CommonException("试卷不存在");
//        }
//        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
//        ExcelImportResult<HrExamResultImport> result =
//            ExcelUtils.importExcel(file, true, HrExamResultImport.class);
//        int size = result.getList().size();
////        if (size == 0 || size > 1000) {
////            throw new CommonException("最少导入一条数据，最多导入1000条数据");
////        }
//        //进度条
//        String key = jwtUserDTO.getId() + "Talent" + redisKey;
//        redisCache.setCacheObject(key, 0, 10, TimeUnit.MINUTES);
//        try {
//            //导入数据
//            this.saveData(result, key, paperId, professionName, examName);
//
//        } catch (Exception e) {
//            redisCache.deleteObject(key);
//            throw new CommonException(e.getMessage());
//        }
//        ImportResultDTO resultDTO;
//        try {
//            resultDTO = ImportResultUtils.writeErrorFile("考试结果" + System.currentTimeMillis(), HrExamResultImport.class, result, fileTempPath);
//            // 判断是否需要上传错误文件
//            if (resultDTO.getFailureFileUrl() != null) {
//                String fileUrl = this.hrAppendixService.uploadErrorImportFile(resultDTO.getFailureFileUrl());
//                resultDTO.setFailureFileUrl(fileUrl);
//            }
//        } catch (IOException e) {
//            log.error("考试结果导入异常:{}", e.getMessage());
//            return ResponseUtil.buildError(e.getMessage());
//        } finally {
//            redisCache.deleteObject(key);
//        }
//        // 操作日志
//        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.EXAM_RESULT.getValue(), BusinessTypeEnum.IMPORT.getKey(), file, JSON.toJSONString(resultDTO), ImportResultDTO.class);
//        return ResponseUtil.buildSuccess(resultDTO);
    }

    /**
     * 导入简章数据
     *
     * @param result
     * @param key
     * @param examName
     */
    private void saveRegistiaonData(ExcelImportResult<HrProfileGradesImport> result, String key, String examName) {
        int listSize = result.getList().size();
        int scale = 0;
        List<HrExamResult> hrExamResults = new ArrayList<>();
        //通过简章名称获取简章id
        QueryWrapper<HrRecruitmentBrochure> hrRecruitmentBrochureQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBrochureQueryWrapper.eq("recruit_brochure_name", examName);
        HrRecruitmentBrochure hrRecruitmentBrochure = hrRecruitmentBrochureService.getOne(hrRecruitmentBrochureQueryWrapper);
        if (hrRecruitmentBrochure == null) {
            throw new CommonException("导入的简章名称不存在");
        }
        //定义map获取导入的岗位，以及导入的成绩类型
        HashMap<String, Integer> hashMap = new HashMap<>();
        for (HrProfileGradesImport hrExamResultImport : result.getList()) {
            HrExamResult hrExamResult = new HrExamResult();
            try {
                BeanUtils.copyProperties(hrExamResultImport, hrExamResult);
                //通过身份证判断员工是否存在
                QueryWrapper<HrTalentStaff> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("certificate_num", hrExamResultImport.getCard());
                List<HrTalentStaff> talentStaffList = hrTalentStaffService.list(queryWrapper);
                if (CollectionUtils.isEmpty(talentStaffList)) {
                    throw new CommonException("系统中不存在该员工");
                }
                //赋值岗位
                HashMap<String, String> stationMap = getStationMap();
                String stationId = stationMap.get(hrExamResultImport.getProfessionName());
                if (StringUtils.isBlank(stationId)) {
                    log.error("导入考试结果发现该{}岗位系统中已经不存在", hrExamResultImport.getProfessionName());
                } else {
                    hrExamResult.setStationId(stationId);
                }
                //获取员工id
                HrTalentStaff hrTalentStaff = talentStaffList.get(0);
                hrExamResult.setStaffId(hrTalentStaff.getId());
                //获取该报名简章下该岗位该员工是否存在
                QueryWrapper<HrRegistrationDetails> hrRegistrationDetailsQueryWrapper = new QueryWrapper<>();
                hrRegistrationDetailsQueryWrapper.eq("brochure_id", hrRecruitmentBrochure.getId());
                hrRegistrationDetailsQueryWrapper.eq("station_name", hrExamResultImport.getProfessionName());
                hrRegistrationDetailsQueryWrapper.eq("staff_id", hrTalentStaff.getId());
                List<HrRegistrationDetails> hrRegistrationDetails = hrRegistrationDetailsRepository.selectList(hrRegistrationDetailsQueryWrapper);
                if (CollectionUtils.isEmpty(hrRegistrationDetails)) {
                    throw new CommonException("该人员不存在报名情况");
                }
                //获取当前简章岗位的试卷id
                //获取该简章该岗位下数据
                QueryWrapper<HrRecruitmentStation> hrRecruitmentStationQueryWrapper = new QueryWrapper<>();
                hrRecruitmentStationQueryWrapper.eq("service_id", hrRecruitmentBrochure.getId());
                hrRecruitmentStationQueryWrapper.eq("recruitment_station_name", hrExamResultImport.getProfessionName());
                List<HrRecruitmentStation> hrRecruitmentStationList = hrRecruitmentStationRepository.selectList(hrRecruitmentStationQueryWrapper);
                String paperId = hrRecruitmentStationList.get(0).getPaperId();
                //如果该招聘简章存在
                String hrRecruitmentBrochureId = hrRecruitmentBrochure.getId();
                //获取招聘岗位信息
                HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationList.get(0);
                //考试形式
                Integer examFormat = hrRecruitmentStation.getExamFormat();
                //通过客户名称获取客户id
                QueryWrapper<HrClient> queryWrapperClient = new QueryWrapper<>();
                queryWrapperClient.eq("client_name", hrExamResultImport.getClientName());
                List<HrClient> hrClientList = hrClientService.list(queryWrapperClient);
                if (CollectionUtils.isEmpty(hrClientList)) {
                    throw new CommonException("系统中不存在该单位名称");
                }
                String clientId = hrClientList.get(0).getId();
                hrExamResult.setClientId(clientId);
                //校验是否能进行导入
                if (hrExamResultImport.getScore() != null) {
                    //笔试成绩有值是否发布了笔试公告
                    List<HrRecruitmentBulletin> hrRecruitmentBulletins = getHrRecruitmentBulletins(hrExamResultImport, hrRecruitmentBrochure);
                    if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                        throw new CommonException("没有发布笔试公告，不能录入笔试成绩");
                    }
                    //根据考试形式来判断下一场公告是否已发布
                    if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getBulletins(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                            throw new CommonException("该简章已经发布了笔试成绩公告，不能录入笔试成绩了");
                        }
                    } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey()) {
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                            throw new CommonException("该简章已经发布了最终成绩公告，不能录入笔试成绩了");
                        }
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins2 = getBulletins(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins2)) {
                            throw new CommonException("该简章已经发布了笔试成绩公告，不能录入笔试成绩了");
                        }
                    } else if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey()) {
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getRecruitmentBulletins(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                            throw new CommonException("该简章已经发布了面试成绩公告，不能录入笔试成绩了");
                        }
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins2 = getBulletins(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins2)) {
                            throw new CommonException("该简章已经发布了笔试成绩公告，不能录入笔试成绩了");
                        }
                    }
                }
                //赋值面试平均成绩
                String interviewScore = hrExamResultImport.getInterviewScore();
                if (StringUtils.isNotBlank(interviewScore)) {
                    //面试成绩有值是否发布了面试公告
                    List<HrRecruitmentBulletin> hrRecruitmentBulletins = getRecruitmentBulletins(hrExamResultImport, hrRecruitmentBrochure);
                    if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                        throw new CommonException("没有发布面试公告，不能录入面试成绩");
                    }
                    //根据考试形式来判断下一场公告是否已发布
                    if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getHrRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                            throw new CommonException("该简章已经发布了面试成绩公告，不能录入面试成绩了");
                        }
                    } else if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey()) {
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                            throw new CommonException("该简章已经发布了最终成绩公告，不能录入面试成绩了");
                        }
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins2 = getHrRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins2)) {
                            throw new CommonException("该简章已经发布了面试成绩公告，不能录入面试成绩了");
                        }
                    } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey()) {
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins1 = getHrRecruitmentBulletins(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins1)) {
                            throw new CommonException("该简章已经发布了笔试公告，不能录入面试成绩了");
                        }
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins2 = getHrRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins2)) {
                            throw new CommonException("该简章已经发布了面试成绩公告，不能录入面试成绩了");
                        }
                    }
                    String regex = ",|，|\\s+";
                    List<String> scoreList = Arrays.asList((interviewScore.split(regex)));
                    //定义面试平均成绩
                    int parseInt = 0;
                    for (String score : scoreList) {
                        try {
                            parseInt += Integer.parseInt(score);
                        } catch (NumberFormatException e) {
                            throw new CommonException("请正确填写面试考官打分，多个考官请已逗号分割");
                        }
                    }
                    BigDecimal bigDecimal = new BigDecimal(parseInt);
                    BigDecimal size = new BigDecimal(scoreList.size());
                    BigDecimal divide = bigDecimal.divide(size, 2, BigDecimal.ROUND_DOWN);
                    hrExamResult.setInterviewScoreResult(divide);
                }
                if (hrExamResultImport.getPhysicalExaminationResult() != null) {
                    //体检成绩有值是否发布了公告
                    LocalDate checkupDate = hrRegistrationDetails.get(0).getCheckupDate();
                    String checkupPlace = hrRegistrationDetails.get(0).getCheckupPlace();
                    if (StringUtils.isBlank(checkupPlace) || checkupDate == null) {
                        throw new CommonException("没有通知体检，不能录入体检成绩");
                    }
                    QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
                    hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
                    hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
                    hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.PHYSICAL_EXAM_RESULTS.getKey());
                    hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
                    List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
                    if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins)) {
                        throw new CommonException("发布了体检结果公告，不能录入体检成绩");
                    }
                }
                //面试权重
                BigDecimal interviewScoreWeight = hrRecruitmentStation.getInterviewScoreWeight();
                //笔试权重
                BigDecimal writtenScoreWeight = hrRecruitmentStation.getWrittenScoreWeight();
                //现根据考试形式来判断如何计算最终成绩
//                        if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
//                            BigDecimal score = hrExamResultImport.getScore();
//                            if (score != null) {
//                                BigDecimal multiply = score.multiply(writtenScoreWeight);
//                                hrExamResult.setFinalResult(multiply);
//                            }
//                        } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
//                            BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
//                            if (interviewScoreResult != null) {
//                                BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
//                                hrExamResult.setFinalResult(multiply);
//                            }
//                        } else {
//                            BigDecimal score = hrExamResultImport.getScore();
//                            BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
//                            if (interviewScoreResult != null && score != null) {
//                                BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
//                                BigDecimal bigDecimal = score.multiply(writtenScoreWeight);
//                                BigDecimal add = multiply.add(bigDecimal);
//                                hrExamResult.setFinalResult(add);
//                            }
//                        }
                if (hrExamResultImport.getExamResult() != null) {
                    if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins = getBulletins(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                            throw new CommonException("没有发布笔试成绩公告，不能录入考察成绩");
                        }
                    } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins = getHrRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                            throw new CommonException("没有发布面试成绩公告，不能录入考察成绩");
                        }
                    } else {
                        List<HrRecruitmentBulletin> hrRecruitmentBulletins = getRecruitmentBulletinList(hrExamResultImport, hrRecruitmentBrochure);
                        if (CollectionUtils.isEmpty(hrRecruitmentBulletins)) {
                            throw new CommonException("没有发布最终成绩公告，不能录入考察成绩");
                        }
                    }
                    QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
                    hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
                    hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
                    hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.INVESTIGATION_RESULTS.getKey());
                    hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
                    List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
                    if (CollectionUtils.isNotEmpty(hrRecruitmentBulletins)) {
                        throw new CommonException("发布了考察结果公告，不能录入考察成绩");
                    }
                }
                //添加考试结果的日志
                JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
                //笔试成绩添加日志
                if (hrExamResultImport.getScore() != null) {
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "笔试成绩--" + hrExamResultImport.getScore(), null, ServiceCenterEnum.SIGN_UP.getKey());
                }
                //面试成绩添加日志
                if (hrExamResult.getInterviewScoreResult() != null) {
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "面试平均成绩--" + hrExamResult.getInterviewScoreResult(), null, ServiceCenterEnum.SIGN_UP.getKey());
                }
                //加试成绩添加日志
                if (hrExamResultImport.getAddResult() != null) {
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "加试成绩--" + hrExamResultImport.getAddResult(), null, ServiceCenterEnum.SIGN_UP.getKey());
                }
                //考察添加日志
                if (hrExamResultImport.getExamResult() != null) {
                    Integer examResult = hrExamResultImport.getExamResult();
                    String results = "";
                    if (examResult == 1) {
                        results = "合格";
                    } else {
                        results = "不合格";
                    }
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "考查结果--" + results, null, ServiceCenterEnum.SIGN_UP.getKey());
                }
                //体检结果日志
                if (hrExamResultImport.getPhysicalExaminationResult() != null) {
                    Integer physicalExaminationResult = hrExamResultImport.getPhysicalExaminationResult();
                    String results = "";
                    if (physicalExaminationResult == 1) {
                        results = "合格";
                    } else {
                        results = "不合格";
                    }
                    this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getRealName() + "录入了" + hrTalentStaff.getName() + "体检结果--" + results, null, ServiceCenterEnum.SIGN_UP.getKey());
                }

                hrExamResult.setExamType(0);

                if (StringUtils.isNotBlank(paperId)) {
                    hrExamResult.setPaperId(paperId);
                } else {
                    hrExamResult.setPaperId(null);
                }
                hrExamResult.setExamName(examName);
                hrExamResult.setProfessionName(hrExamResultImport.getProfessionName());
                //判断时新增还是修改
                QueryWrapper<HrExamResult> qw = new QueryWrapper<>();
                //qw.eq(StringUtils.isNotBlank(paperId),"paper_id", paperId);
                qw.eq("profession_name", hrExamResultImport.getProfessionName());
                qw.eq("exam_name", examName);
                qw.eq("staff_id", hrTalentStaff.getId());
                List<HrExamResult> list = list(qw);
                if (CollectionUtils.isEmpty(list)) {
                    save(hrExamResult);
                    hrExamResults.add(hrExamResult);
                } else {
                    hrExamResult.setId(list.get(0).getId());
                    updateById(hrExamResult);
                    hrExamResults.add(hrExamResult);
                }
                ///计算通过率
                if (StringUtils.isNotBlank(paperId)) {
                    examNumber(paperId, hrExamResultImport.getProfessionName(), examName);
                }
                //获取导入的岗位，以及导入的成绩
                String professionName = hrExamResultImport.getProfessionName();
                if (!hashMap.containsKey(professionName)) {
                    if (hrExamResultImport.getScore() != null) {
                        hashMap.put(professionName, 1);
                    }
                    if (hrExamResultImport.getInterviewScore() != null) {
                        hashMap.put(professionName, 2);
                    }
                    if (hrExamResultImport.getExamResult() != null) {
                        hashMap.put(professionName, 3);
                    }
                    if (hrExamResultImport.getPhysicalExaminationResult() != null) {
                        hashMap.put(professionName, 4);
                    }
                }
                //修改报名情况状态
//                updateTyde(hrExamResultImport.getProfessionName(), hrRecruitmentBrochureId, examName, hrTalentStaff.getId());
            } catch (Exception e) {
                log.error("保存考试结果异常:{}", e.getMessage());
                hrExamResultImport.setErrorMsg(e.getMessage());
            } finally {
                scale++;
                int i = CalculateUtils.calculationProgress(scale, listSize);
                redisCache.setCacheObject(key, i, 10, TimeUnit.MINUTES);
            }
        }
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(hashMap)) {
            for (String professionName : hashMap.keySet()) {
                this.updateApplicantStatus(hrRecruitmentBrochure, professionName, hashMap.get(professionName));
            }
        }
    }

    /**
     * 修改报名人员状态
     *
     * @param hrRecruitmentBrochure
     * @param professionName
     * @param achievementType
     */
    @Override
    public void updateApplicantStatus(HrRecruitmentBrochure hrRecruitmentBrochure, String professionName, Integer achievementType) {
        List<HrRecruitmentStation> hrRecruitmentStations = hrRecruitmentStationRepository.selectList(new QueryWrapper<HrRecruitmentStation>()
            .eq("service_id", hrRecruitmentBrochure.getId()).eq("recruitment_station_name", professionName));
        for (HrRecruitmentStation hrRecruitmentStation : hrRecruitmentStations) {
            //招聘人数
            BigDecimal recruitmentPeopleNumber = new BigDecimal(String.valueOf(hrRecruitmentStation.getRecruitmentPeopleNumber()));
            //报名人根据笔试成绩倒序排序，取出进入考察的人员，其他人全部是笔试不合格
            List<Integer> statusList = new ArrayList<>();
            if (achievementType == 1 && (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())
                || hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey()))) {
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_WRITTEN_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
            } else if (achievementType == 1 && hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_WRITTEN_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INTERVIEW_SCOPE.getKey());
            } else if (achievementType == 2 && (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())
                || hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey()))) {
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INTERVIEW_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
            } else if (achievementType == 2 && hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INTERVIEW_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.NO_WRITTEN_EXAM_SCOPE.getKey());
            } else if (achievementType == 3) {//todo 该状态
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_INVESTIGATE_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INSPECTION_RESULTS.getKey());
            } else if (achievementType == 4) {//todo 该状态
                statusList.add(RecruitmentBrochure.RegistrationStatus.TO_BE_PHYSICAL_EXAM_RESULTS.getKey());
                statusList.add(RecruitmentBrochure.RegistrationStatus.FAILED_PHYSICAL_EXAM.getKey());
            }
            List<HrRegistrationDetailsDTO> hrRegistrationDetailsDTOS = hrRegistrationDetailsRepository.findDetailsWrittenExam(hrRecruitmentBrochure.getId(), hrRecruitmentStation.getId(), statusList);
            this.calculateFinalScore(hrRecruitmentBrochure, hrRecruitmentStation, hrRegistrationDetailsDTOS);
            //判断这次导入是什么成绩
            switch (achievementType) {
                case 1://笔试成绩
                    //判断岗位形式是什么形式
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getInvestigationRatio()));
                        if (number != null) {
                            //笔试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //笔试成绩合格
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有笔试成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = qualifiedList.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getScore)).collect(Collectors.toList());
                                Collections.reverse(registrationDetailsDTOS);
                                this.inspectorInfo(registrationDetailsDTOS, number, hrRecruitmentStation);
                            }
                        }
                    }
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getInvestigationRatio()));
                        if (number != null) {
                            //最终成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //笔试成绩合格
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有最终成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = qualifiedList.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getFinalResult)).collect(Collectors.toList());
                                Collections.reverse(registrationDetailsDTOS);
                                this.inspectorInfo(registrationDetailsDTOS, number, hrRecruitmentStation);
                            }
                        }
                    }
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                        //计算该岗位下进入考察的人员  根据晋级比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getPromotedRatio()));
                        if (number != null) {
                            //笔试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_WRITTEN_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //笔试成绩合格,大于笔试及格线的人数
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getScore() != null && ls.getScore().compareTo(hrRecruitmentStation.getWrittenPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有笔试成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = this.hrRecruitmentBulletinService.mergerList(qualifiedList, achievementType);
                                //进入面试人员
                                List<HrRegistrationDetailsDTO> investigateDetailsDTOS = registrationDetailsDTOS.stream().filter(ls -> ls.getRank() <= number).collect(Collectors.toList());
                                for (HrRegistrationDetailsDTO hrRegistrationDetailsDTO : investigateDetailsDTOS) {
                                    HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                    hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INTERVIEW.getKey());
                                    hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                                }
                                List<HrRegistrationDetailsDTO> detailsDTOS = registrationDetailsDTOS.stream().filter(d1 -> investigateDetailsDTOS.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(detailsDTOS)) {
                                    for (HrRegistrationDetailsDTO hrRegistrationDetailsDTO : detailsDTOS) {
                                        HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                        hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.NO_INTERVIEW_SCOPE.getKey());
                                        hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 2://面试成绩
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getInvestigationRatio()));
                        if (number != null) {
                            //面试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) == -1).collect(Collectors.toList());
                            for (HrRegistrationDetailsDTO hrRegistrationDetailsDTO : failList) {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            }
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //面试成绩合格 ，大于面试及格线
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有面试成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = qualifiedList.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getInterviewScoreResult)).collect(Collectors.toList());
                                Collections.reverse(registrationDetailsDTOS);//根据面试成绩排序
                                this.inspectorInfo(registrationDetailsDTOS, number, hrRecruitmentStation);
                            }
                        }
                    }
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION_BEFORE_INTERVIEW.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getInvestigationRatio()));
                        if (number != null) {
                            //面试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //面试成绩合格
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有最终成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = qualifiedList.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getFinalResult)).collect(Collectors.toList());
                                Collections.reverse(registrationDetailsDTOS);
                                this.inspectorInfo(registrationDetailsDTOS, number, hrRecruitmentStation);
                            }
                        }
                    }
                    if (hrRecruitmentStation.getExamFormat().equals(RecruitmentBrochure.ExamFormat.INTERVIEW_BEFORE_WRITTEN_EXAMINATION.getKey())) {
                        //计算该岗位下进入考察的人员  根据考察比例计算
                        //根据考察比例计算要取几个人
                        Integer number = DataUtils.compareNumber(recruitmentPeopleNumber.multiply(hrRecruitmentStation.getPromotedRatio()));
                        if (number != null) {
                            //面试成绩不合格人数
                            List<HrRegistrationDetailsDTO> failList = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) == -1).collect(Collectors.toList());
                            failList.forEach(hrRegistrationDetailsDTO -> {
                                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INTERVIEW_RESULTS.getKey());
                                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                            });
                            List<HrRegistrationDetailsDTO> list = hrRegistrationDetailsDTOS.stream().filter(d1 -> failList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                            //笔试成绩合格,大于笔试及格线的人数
                            List<HrRegistrationDetailsDTO> qualifiedList = list.stream().filter(ls -> ls.getInterviewScoreResult() != null && ls.getInterviewScoreResult().compareTo(hrRecruitmentStation.getInterviewPassLine()) > -1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(qualifiedList)) {
                                //所有笔试成绩合格人数
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOS = this.hrRecruitmentBulletinService.mergerList(qualifiedList, achievementType);
                                //进入笔试人员
                                List<HrRegistrationDetailsDTO> investigateDetailsDTOS = registrationDetailsDTOS.stream().filter(ls -> ls.getRank() <= number).collect(Collectors.toList());
                                investigateDetailsDTOS.forEach(hrRegistrationDetailsDTO -> {//进入笔试人员信息
                                    HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                    hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_WRITTEN.getKey());
                                    hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                                });
                                List<HrRegistrationDetailsDTO> registrationDetailsDTOList = registrationDetailsDTOS.stream().filter(d1 -> investigateDetailsDTOS.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(registrationDetailsDTOList)) {
                                    registrationDetailsDTOList.forEach(hrRegistrationDetailsDTO -> {
                                        HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                                        hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.NO_WRITTEN_EXAM_SCOPE.getKey());
                                        hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                                    });
                                }
                            }
                        }
                    }
                    break;
                case 3://考察成绩
                    List<HrRegistrationDetailsDTO> qualifiedListExamResult = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getExamResult() != null && ls.getExamResult() == 1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(qualifiedListExamResult)) {
                        qualifiedListExamResult.forEach(hrRegistrationDetailsDTO -> {
                            HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                            hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_PHYSICAL.getKey());
                            hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                        });
                    }
                    List<HrRegistrationDetailsDTO> unqualifiedListExamResult = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getExamResult() != null && ls.getExamResult() == 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(unqualifiedListExamResult)) {
                        unqualifiedListExamResult.forEach(hrRegistrationDetailsDTO -> {
                            HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                            hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.UNQUALIFIED_INSPECTION_RESULTS.getKey());
                            hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                        });
                    }
                    break;
                case 4://体检成绩
                    List<HrRegistrationDetailsDTO> qualifiedListPhysicalExaminationResult = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getPhysicalExaminationResult() != null && ls.getPhysicalExaminationResult() == 1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(qualifiedListPhysicalExaminationResult)) {
                        qualifiedListPhysicalExaminationResult.forEach(hrRegistrationDetailsDTO -> {
                            HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                            hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE_FORMULA_EMPLOYED.getKey());
                            hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                        });
                    }
                    List<HrRegistrationDetailsDTO> unqualifiedListPhysicalExaminationResult = hrRegistrationDetailsDTOS.stream().filter(ls -> ls.getPhysicalExaminationResult() != null && ls.getPhysicalExaminationResult() == 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(unqualifiedListPhysicalExaminationResult)) {
                        unqualifiedListPhysicalExaminationResult.forEach(hrRegistrationDetailsDTO -> {
                            HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                            hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.FAILED_PHYSICAL_EXAM.getKey());
                            hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                        });
                    }
                    break;
            }
        }

    }

    /**
     * 进入考察人员
     *
     * @param registrationDetailsDTOS
     * @param number
     * @param hrRecruitmentStation
     */
    private void inspectorInfo(List<HrRegistrationDetailsDTO> registrationDetailsDTOS, Integer number, HrRecruitmentStation hrRecruitmentStation) {
        //进入考察人员
        List<HrRegistrationDetailsDTO> investigateDetailsDTOS = registrationDetailsDTOS.subList(0, Math.min(registrationDetailsDTOS.size(), number));
        //最高得分
        BigDecimal max = Collections.max(investigateDetailsDTOS.stream().map(HrRegistrationDetailsDTO::getFinalResult).distinct().collect(Collectors.toList()));
        //校验是否是等额考察
        //判断最高得分的人数是否大于招聘人数，如果大于招聘人数，所有人都进入考察范围
        List<HrRegistrationDetailsDTO> dtoList = investigateDetailsDTOS.stream().filter(ls -> ls.getFinalResult().compareTo(max) == 0).collect(Collectors.toList());
        if (dtoList.size() > hrRecruitmentStation.getRecruitmentPeopleNumber()) {
            dtoList.forEach(hrRegistrationDetailsDTO -> {
                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
            });
        } else {//小于招聘人数，判断哪些人进入等额考察，哪些人进入
            if (hrRecruitmentStation.getIsEqualInvestigate()) {
                //如果开启了等额考察，取出和招聘人数相等的人改为已进入等额考察，剩余的人为已进入考察范围
                List<HrRegistrationDetailsDTO> subList = investigateDetailsDTOS.subList(0, Math.min(investigateDetailsDTOS.size(), hrRecruitmentStation.getRecruitmentPeopleNumber()));
                subList.forEach(hrRegistrationDetailsDTO -> {
                    HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                    hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.ENTER_EQUIVALENT_INVESTIGATION.getKey());
                    hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                });
                List<HrRegistrationDetailsDTO> list = investigateDetailsDTOS.stream().filter(d1 -> subList.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(hrRegistrationDetailsDTO -> {
                        HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                        hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                        hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                    });
                }
            } else {
                investigateDetailsDTOS.forEach(hrRegistrationDetailsDTO -> {
                    HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                    hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.TO_BE__NOTIFIED_INVESTIGATE.getKey());
                    hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
                });
            }
        }
        List<HrRegistrationDetailsDTO> noInvestigationList = registrationDetailsDTOS.stream().filter(d1 -> investigateDetailsDTOS.stream().noneMatch(d2 -> Objects.equals(d1.getId(), d2.getId()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noInvestigationList)) {
            noInvestigationList.forEach(hrRegistrationDetailsDTO -> {
                HrRegistrationDetails hrRegistrationDetails = hrRegistrationDetailsMapper.toEntity(hrRegistrationDetailsDTO);
                hrRegistrationDetails.setStatus(RecruitmentBrochure.RegistrationStatus.NO_INVESTIGATION.getKey());
                hrRegistrationDetailsRepository.updateById(hrRegistrationDetails);
            });
        }
    }

    /**
     * 计算报名人员最终成绩
     *
     * @param hrRecruitmentBrochure
     * @param hrRecruitmentStation
     * @param hrRegistrationDetailsDTOS
     */
    private void calculateFinalScore(HrRecruitmentBrochure hrRecruitmentBrochure, HrRecruitmentStation hrRecruitmentStation, List<HrRegistrationDetailsDTO> hrRegistrationDetailsDTOS) {
        for (HrRegistrationDetailsDTO hrRegistrationDetailsDTO : hrRegistrationDetailsDTOS) {
            //查询报名人员考试结果实体类
            List<HrExamResult> hrExamResults = hrExamResultRepository.selectList(new QueryWrapper<HrExamResult>().eq("exam_name", hrRecruitmentBrochure.getRecruitBrochureName())
                .eq("profession_name", hrRecruitmentStation.getRecruitmentStationName()).eq("staff_id", hrRegistrationDetailsDTO.getStaffId()));
            HrExamResult hrExamResult = null;
            if (CollectionUtils.isNotEmpty(hrExamResults)) {
                hrExamResult = hrExamResults.get(0);
            }

            if (hrExamResult != null) {
                Integer examFormat = hrRecruitmentStation.getExamFormat();
                BigDecimal writtenScoreWeight = hrRecruitmentStation.getWrittenScoreWeight();
                BigDecimal interviewScoreWeight = hrRecruitmentStation.getInterviewScoreWeight();
                //现根据考试形式来判断如何计算最终成绩
                if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
                    BigDecimal score = hrExamResult.getScore();
                    if (score != null) {
                        BigDecimal multiply = score.multiply(writtenScoreWeight);
                        hrExamResult.setFinalResult(multiply);
                        hrRegistrationDetailsDTO.setFinalResult(multiply);
                    }
                } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
                    BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
                    if (interviewScoreResult != null) {
                        BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
                        hrExamResult.setFinalResult(multiply);
                        hrRegistrationDetailsDTO.setFinalResult(multiply);
                    }
                } else {
                    BigDecimal score = hrExamResult.getScore();
                    BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
                    if (interviewScoreResult != null && score != null) {
                        BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
                        BigDecimal bigDecimal = score.multiply(writtenScoreWeight);
                        BigDecimal add = multiply.add(bigDecimal);
                        hrExamResult.setFinalResult(add);
                        hrRegistrationDetailsDTO.setFinalResult(add);
                    }
                }
                this.hrExamResultRepository.updateHrExamResult(hrExamResult);
            }
        }
    }

    private List<HrRegistrationDetailsDTO> calculateRanking(List<HrRegistrationDetailsDTO> list, Integer achievementType) {
        List<HrRegistrationDetailsDTO> collect = new ArrayList<>();
        switch (achievementType) {
            case 1://笔试
                collect = list.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getScore)).collect(Collectors.toList());
                break;
            case 2://面试
                collect = list.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getInterviewScoreResult)).collect(Collectors.toList());
                break;
            case 3:
                collect = list.stream().sorted(Comparator.comparing(HrRegistrationDetailsDTO::getFinalResult)).collect(Collectors.toList());
                break;
            default:
                collect = list;
                break;
        }

        Collections.reverse(collect);
        Integer index = 1;
        BigDecimal maxScore = null;
        for (int i = 0; i < list.size(); i++) {
            if (i == 0) {
                collect.get(i).setRank(index);
                maxScore = collect.get(i).getScore();
            } else if (Objects.equals(collect.get(i).getScore(), maxScore)) {
                collect.get(i).setRank(index);
            } else {
                index++;
                collect.get(i).setRank(index);
                maxScore = collect.get(i).getScore();
            }
        }
        return collect;
    }

    /**
     * 最终成绩公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getRecruitmentBulletinList(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.FINAL_ACHIEVEMENT.getKey());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
        List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
        return hrRecruitmentBulletins;
    }

    /**
     * 面试成绩公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getHrRecruitmentBulletinList(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.INTERVIEW_ACHIEVEMENT.getKey());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
        return hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
    }

    /**
     * 笔试成绩公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getBulletins(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("achievement_type", RecruitmentBrochure.AchievementType.WRITTEN_ACHIEVEMENT.getKey());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.ACHIEVEMENT_ANNOUNCEMENT.getKey());
        return hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
    }

    /**
     * 查询面试公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getRecruitmentBulletins(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.INTERVIEW_ANNOUNCEMENT.getKey());
        List<HrRecruitmentBulletin> hrRecruitmentBulletins = hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
        return hrRecruitmentBulletins;
    }

    /**
     * 查询笔试公告
     *
     * @param hrExamResultImport
     * @param hrRecruitmentBrochure
     * @return
     */
    private List<HrRecruitmentBulletin> getHrRecruitmentBulletins(HrProfileGradesImport hrExamResultImport, HrRecruitmentBrochure hrRecruitmentBrochure) {
        QueryWrapper<HrRecruitmentBulletin> hrRecruitmentBulletinQueryWrapper = new QueryWrapper<>();
        hrRecruitmentBulletinQueryWrapper.eq("recruitment_brochure_id", hrRecruitmentBrochure.getId());
        hrRecruitmentBulletinQueryWrapper.like("recruitment_station_name", hrExamResultImport.getProfessionName());
        hrRecruitmentBulletinQueryWrapper.eq("notice_type", RecruitmentBrochure.NoticeType.WRITTEN_ANNOUNCEMENT.getKey());
        return hrRecruitmentBulletinRepository.selectList(hrRecruitmentBulletinQueryWrapper);
    }

    @Override
    public String importExamResultTemplate(HttpServletResponse response) {
//        ExportParams params = new ExportParams(null, "考试结果");
//        //params.setStyle(ExcelStyleUtils.class);
//        params.setType(ExcelType.XSSF);
//        List<HrExamResultTemplate> list = new ArrayList<>();
//        HrExamResultTemplate hrExamResultTemplate = new HrExamResultTemplate();
//        LocalDateTime time = LocalDateTime.of(2021, 12, 11, 12, 23);
//        //hrExamResultTemplate.setExamDate(time);
//        //hrExamResultTemplate.setAppendixUrl("url");
//        hrExamResultTemplate.setCard("2306221998080888882");
//        //hrExamResultTemplate.setEvaluation("很好");
//        hrExamResultTemplate.setScore(new BigDecimal(80));
//        hrExamResultTemplate.setClientName("xx单位");
//        hrExamResultTemplate.setInterviewExamStartTime(time);
//        hrExamResultTemplate.setInterviewLocation("xx地点");
//        hrExamResultTemplate.setWrittenExamStartTime(time);
//        hrExamResultTemplate.setPhysicalExamStartTime(time);
//        hrExamResultTemplate.setSurveyExamStartTime(time);
//        hrExamResultTemplate.setWrittenLocation("xx地点");
//        hrExamResultTemplate.setPhysicalLocation("xx地点");
//        hrExamResultTemplate.setSurveyLocation("xx地点");
//        //hrExamResultTemplate.setProfessionName("xx岗位");
//        //hrExamResultTemplate.setPaperName("入职考试");
//        hrExamResultTemplate.setPhone("15765778315");
//        hrExamResultTemplate.setStudentsName("张三");
//        hrExamResultTemplate.setInterviewScore("99,23");
//        //hrExamResultTemplate.setInterviewScoreResult(new BigDecimal(80));
//        hrExamResultTemplate.setAddResult(new BigDecimal(80));
//        hrExamResultTemplate.setExamResult(1);
//        hrExamResultTemplate.setInterviewResult(1);
//        hrExamResultTemplate.setPhysicalExaminationResult(1);
//        list.add(hrExamResultTemplate);
//        Workbook workbook = ExcelExportUtil.exportExcel(params, HrExamResultTemplate.class, list);
//        ExcelUtils.downLoadExcel("考试结果导入模板.xlsx", response, workbook);
        //ExcelUtils.exportExcelWithNoHeader(list, "考试结果导入模板", "考试结果", HrExamResultTemplate.class, response);
        return excelPrefix + "考试结果导入模板.xlsx";
    }

    /**
     * 考试结果详情
     *
     * @param hrExamResultDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage<HrQuestionDTO> examResultDetail(HrExamResultDTO hrExamResultDTO, Long pageNumber, Long pageSize) {
        Page<HrQuestion> page = new Page<>(pageNumber, pageSize);
        IPage<HrQuestionDTO> hrQuestionDTOIPage = hrExamResultRepository.examResultDetail(page, hrExamResultDTO);
        for (HrQuestionDTO questionDTO : hrQuestionDTOIPage.getRecords()) {
            //赋值类型
            Map<Integer, String> questionType = codeTableService.findCodeTableByInnerName("questionType");
            questionDTO.setQuestionTypeName(questionType.get(questionDTO.getQuestionType()));
        }
        return hrQuestionDTOIPage;
    }

    /**
     * 考试结果导出
     *
     * @param ids
     * @param httpServletResponse
     * @return
     */
    @Override
    public String exportExamResults(List<String> ids, HttpServletResponse httpServletResponse) {
        List<HrExamResultTemplate> list = hrExamResultRepository.exportExamResults(ids);
        // ExcelUtils.exportExcel(list, "考核结果信息", HrExamResultTemplate.class, httpServletResponse);
        int listSize = list.size();
        String fileUrl = this.hrAppendixService.uploadExportFile(list, "考核结果信息", HrExamResultTemplate.class);
        // 操作日志
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.EXAM_RESULT.getValue(), BusinessTypeEnum.EXPORT.getKey(), JSON.toJSONString(ids), listSize, fileUrl);
        return fileUrl;
    }

    /**
     * 微信员工入职考试提交结果
     *
     * @param wxExamResultDTOList
     */
    @Override
    public HrExamResult wxExamResult(List<WxExamResultDTO> wxExamResultDTOList) {
        if (CollectionUtils.isEmpty(wxExamResultDTOList)) {
            throw new CommonException("考试结果不能为空");
        }
        //获取试卷id
        String paperId = wxExamResultDTOList.get(0).getPaperId();
        //先判断结果试卷中是否存在该试卷 不存在新建
        List<HrExam> hrExamList = hrExamService.list(new QueryWrapper<HrExam>().eq("paper_id", paperId));
        if (CollectionUtils.isEmpty(hrExamList)) {
            HrExamDTO hrExamDTO = new HrExamDTO();
            hrExamDTO.setPaperId(paperId);
            hrExamService.createHrExam(hrExamDTO);
        }
        //获取考生信息
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        String name = jwtMiniDTO.getName();
        String phone = jwtMiniDTO.getPhone();
        String clientId = jwtMiniDTO.getClientId();
        String certificateNum = jwtMiniDTO.getCertificateNum();
        String staffId = jwtMiniDTO.getId();
        //计算分数
        //int sore = 0;
        BigDecimal sore = BigDecimal.ZERO;
        //获取考试时间
        //LocalDateTime examDate = wxExamResultDTOList.get(0).getExamDate();
        LocalDateTime examDate = LocalDateTime.now();
        for (WxExamResultDTO wxExamResultDTO : wxExamResultDTOList) {
            //正确答案
            String correct = wxExamResultDTO.getCorrect();
            //考生答案
            List<String> studentAnswer = wxExamResultDTO.getStudentAnswer();
            //定义答案是否正确
            Boolean bool = true;
            if (CollectionUtils.isEmpty(studentAnswer)) {
                bool = false;
            }
            String regex = ",|，|\\s+";
            List<String> correctList = Arrays.asList((correct.split(regex)));
            if (CollectionUtils.isEmpty(correctList)) {
                bool = false;
            }
            if (studentAnswer.size() == correctList.size()) {
                for (String answer : studentAnswer) {
                    if (StringUtils.isBlank(answer)) {
                        bool = false;
                    } else {
                        if (!correct.contains(answer)) {
                            bool = false;
                            break;
                        }
                    }

                }
            } else {
                bool = false;
            }

            if (bool) {
                //sore += wxExamResultDTO.getScore();
                BigDecimal score = wxExamResultDTO.getScore();
                sore = sore.add(score);
            }

        }
        //将此人添加到考试结果中
        HrExamResult hrExamResult = new HrExamResult();
        hrExamResult.setPaperId(paperId);
        hrExamResult.setExamType(1);
        hrExamResult.setCard(certificateNum);
        hrExamResult.setClientId(clientId);
        hrExamResult.setExamDate(examDate);
        hrExamResult.setStaffId(staffId);
        hrExamResult.setInterviewLink(5);
        //获取单位名称
        HrClient hrClient = hrClientService.getById(clientId);
        if (hrClient != null) {
            hrExamResult.setClientName(hrClient.getClientName());
        }
        hrExamResult.setPhone(phone);
        hrExamResult.setScore(sore);
        hrExamResult.setStudentsName(name);
        //查询试卷及格线
        HrPaperManagement hrPaperManagement = hrPaperManagementService.getById(paperId);
        if (hrPaperManagement.getPassLine() != null) {
            hrExamResult.setPassLine(hrPaperManagement.getPassLine());
        }
        int score = sore.intValue();
        if (score >= hrPaperManagement.getPassLine()) {
            hrExamResult.setInterviewResult(1);
        } else {
            hrExamResult.setInterviewResult(2);
        }
        hrExamResultRepository.insert(hrExamResult);
        //将每道题加到结果详情表
        for (WxExamResultDTO wxExamResultDTO : wxExamResultDTOList) {
            HrExamResultDetails hrExamResultDetails = new HrExamResultDetails();
            hrExamResultDetails.setExamResultId(hrExamResult.getId());
            hrExamResultDetails.setQuestionId(wxExamResultDTO.getQuestionId());
            hrExamResultDetails.setStudentAnswer(String.join(",", wxExamResultDTO.getStudentAnswer()));
            hrExamResultDetailsService.save(hrExamResultDetails);
        }

        //获取考试详情
//        HrExamResultDTO hrExamResultDTO = new HrExamResultDTO();
//        hrExamResultDTO.setPaperId(paperId);
//        hrExamResultDTO.setExamResultId(hrExamResult.getId());
//        IPage<HrQuestionDTO> hrQuestionDTOIPage = examResultDetail(hrExamResultDTO, 1L, 1000L);
//        hrExamResult.setHrQuestionDTOList(hrQuestionDTOIPage.getRecords());
        //重新计算这张考试的及格数和通过率
        examNumber(paperId, null, null);
        //将这张的试卷使用次数+1
        int usageSum = (int) hrPaperManagement.getUsageSum();
        hrPaperManagement.setUsageSum(usageSum + 1);
        hrPaperManagementService.updateById(hrPaperManagement);
        return hrExamResult;
    }

    /**
     * 微信员工入职考试获取结果
     *
     * @return
     */
    @Override
    public HrExamResult hrExamGetResult() {
        //获取入职试卷id
        QueryWrapper<HrPaperManagement> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_preset", "1");
        List<HrPaperManagement> list = hrPaperManagementService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new CommonException("入职考试试卷不存在");
        }
        HrPaperManagement hrPaperManagement = list.get(0);
        //获取考试结果id
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        String certificateNum = jwtMiniDTO.getCertificateNum();
        QueryWrapper<HrExamResult> qw = new QueryWrapper<>();
        qw.eq("card", certificateNum);
        qw.eq("paper_id", hrPaperManagement.getId());
        qw.orderByDesc("created_date");
        qw.last("limit 1");
        HrExamResult hrExamResult = getOne(qw);
        if (null == hrExamResult) {
            throw new CommonException("您还没有进行考试");
        }
        HrExamResultDTO hrExamResultDTO = new HrExamResultDTO();
        hrExamResultDTO.setPaperId(hrPaperManagement.getId());
        hrExamResultDTO.setExamResultId(hrExamResult.getId());
        IPage<HrQuestionDTO> hrQuestionDTOIPage = examResultDetail(hrExamResultDTO, 1L, 1000L);
        hrExamResult.setHrQuestionDTOList(hrQuestionDTOIPage.getRecords());
        hrExamResult.setPassLine(hrPaperManagement.getPassLine());
        return hrExamResult;
    }

    /**
     * 获取应试经历
     *
     * @return
     */
    @Override
    public List<HrExamResultDTO> examResultExperience(String staffId) {
        QueryWrapper<HrExamResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("staff_id", staffId);
        queryWrapper.eq("is_synchronize", 1);
        List<HrExamResult> list = list(queryWrapper);
        List<HrExamResultDTO> hrExamResultDTOList = hrExamResultMapper.toDto(list);
        for (HrExamResultDTO hrExamResultDTO : hrExamResultDTOList) {
            if (hrExamResultDTO.getInterviewLink() != null) {
                Map<Integer, String> interviewLink = codeTableService.findCodeTableByInnerName("interviewLink");//应试环节
                hrExamResultDTO.setInterviewLinkLabel(interviewLink.get(hrExamResultDTO.getInterviewLink()));
            }
            if (hrExamResultDTO.getInterviewResult() != null) {
                Map<Integer, String> interviewResult = codeTableService.findCodeTableByInnerName("interviewResult");//应试结果
                hrExamResultDTO.setInterviewResultLabel(interviewResult.get(hrExamResultDTO.getInterviewResult()));
            }
        }

        return hrExamResultDTOList;
    }

    /**
     * 简章成绩导入模板
     *
     * @param response
     */
    @Override
    public String importProfileGradesTemplate(HttpServletResponse response) {
//        ExportParams params = new ExportParams(null, "简章成绩导入模板");
//        //params.setStyle(ExcelStyleUtils.class);
//        params.setType(ExcelType.XSSF);
//        List<HrProfileGradesTemplate> list = new ArrayList<>();
//        HrProfileGradesTemplate hrExamResultTemplate = new HrProfileGradesTemplate();
//        LocalDateTime time = LocalDateTime.of(2021, 12, 11, 12, 23, 11);
//        //hrExamResultTemplate.setExamDate(time);
//        //hrExamResultTemplate.setAppendixUrl("url");
//        hrExamResultTemplate.setCard("2306221998080888882");
//        //hrExamResultTemplate.setEvaluation("很好");
//        hrExamResultTemplate.setScore(new BigDecimal(80));
//        hrExamResultTemplate.setClientName("xx单位");
//        hrExamResultTemplate.setProfessionName("xx岗位");
//        //hrExamResultTemplate.setPaperName("入职考试");
//        //hrExamResultTemplate.setPhone("15765778315");
//        hrExamResultTemplate.setStudentsName("张三");
//        hrExamResultTemplate.setInterviewScore("99,23");
//        //hrExamResultTemplate.setInterviewScoreResult(new BigDecimal(80));
//        hrExamResultTemplate.setAddResult(new BigDecimal(80));
//        hrExamResultTemplate.setExamResult(1);
//        hrExamResultTemplate.setPhysicalExaminationResult(1);
//        list.add(hrExamResultTemplate);
//        Workbook workbook = ExcelExportUtil.exportExcel(params, HrProfileGradesTemplate.class, list);
//        ExcelUtils.downLoadExcel("简章成绩导入模板.xlsx", response, workbook);
        return excelPrefix + "简章成绩导入模板.xlsx";

    }

    @Override
    public String importProfileGrades(MultipartFile file, String examName) {
        // 设置进度条
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        String redisKey = RedisKeyEnum.progressBar.PROFILE_GRADES.getValue() + RandomUtil.generateId();
        redisCache.setCacheObject(redisKey, 0, 30, TimeUnit.MINUTES);
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new CommonException("文件流获取失败！");
        }
        this.questionExamComponent.profileGradesImport(inputStream, redisKey, jwtUserDTO, examName);
        return redisKey;
//        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
//
//        ExcelImportResult<HrProfileGradesImport> result =
//            ExcelUtils.importExcel(file, true, HrProfileGradesImport.class);
////        int size = result.getList().size();
////        if (size == 0 || size > 1000) {
////            throw new CommonException("最少导入一条数据，最多导入1000条数据");
////        }
//        //进度条
//        String key = jwtUserDTO.getId() + "Talent" + redisKey;
//        redisCache.setCacheObject(key, 0, 10, TimeUnit.MINUTES);
//        try {
//            //导入数据
//            this.saveRegistiaonData(result, key, examName);
//        } catch (Exception e) {
//            redisCache.deleteObject(key);
//            throw new CommonException(e.getMessage());
//        }
//        ImportResultDTO resultDTO;
//        try {
//            resultDTO = ImportResultUtils.writeErrorFile("考试结果" + System.currentTimeMillis(), HrProfileGradesImport.class, result, fileTempPath);
//            // 判断是否需要上传错误文件
//            if (resultDTO.getFailureFileUrl() != null) {
//                String fileUrl = this.hrAppendixService.uploadErrorImportFile(resultDTO.getFailureFileUrl());
//                resultDTO.setFailureFileUrl(fileUrl);
//            }
//        } catch (IOException e) {
//            log.error("考试结果导入异常:{}", e.getMessage());
//            return ResponseUtil.buildError(e.getMessage());
//        } finally {
//            redisCache.deleteObject(key);
//        }
//        // 操作日志
//        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.RECRUITMENT_RESULT.getValue(), BusinessTypeEnum.IMPORT.getKey(), file, JSON.toJSONString(resultDTO), ImportResultDTO.class);
//        return ResponseUtil.buildSuccess(resultDTO);
    }

    /**
     * 更新考试结果表
     *
     * @param hrExamResultDTO
     */
    @Override
    public void updateBatch(List<HrExamResult> hrExamResultDTO) {
        for (HrExamResult hrExamResult : hrExamResultDTO) {
            updateById(hrExamResult);
            //同时维护考试详情表
            //笔试策划成绩
            BigDecimal score = hrExamResult.getScore();
            if (score != null) {
                //对应面试环节 1笔试 2 面试 7 考察  8体检
                QueryWrapper<HrExamDetails> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("exam_result_id", hrExamResult.getId());
                queryWrapper.eq("exam_type", 1);
                List<HrExamDetails> hrExamDetailsList = hrExamDetailsRepository.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(hrExamDetailsList)) {
                    HrExamDetails hrExamDetails = hrExamDetailsList.get(0);
                    hrExamDetails.setScore(score);
                    int passLine = hrPaperManagementService.getById(hrExamResult.getPaperId()).getPassLine();
                    if (score.intValue() >= passLine) {
                        hrExamDetails.setIsPassed(1);
                    } else {
                        hrExamDetails.setIsPassed(0);
                    }
                    hrExamDetailsRepository.updateById(hrExamDetails);
                }
            }
            if (hrExamResult.getInterviewScoreResult() != null) {
                //对应面试环节 1笔试 2 面试 7 考察  8体检
                QueryWrapper<HrExamDetails> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("exam_result_id", hrExamResult.getId());
                queryWrapper.eq("exam_type", 2);
                List<HrExamDetails> hrExamDetailsList = hrExamDetailsRepository.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(hrExamDetailsList)) {
                    HrExamDetails hrExamDetails = hrExamDetailsList.get(0);
                    hrExamDetails.setScore(hrExamResult.getInterviewScoreResult());
                    hrExamDetailsRepository.updateById(hrExamDetails);
                }
            }
            if (hrExamResult.getExamResult() != null) {
                //对应面试环节 1笔试 2 面试 7 考察  8体检
                QueryWrapper<HrExamDetails> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("exam_result_id", hrExamResult.getId());
                queryWrapper.eq("exam_type", 7);
                List<HrExamDetails> hrExamDetailsList = hrExamDetailsRepository.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(hrExamDetailsList)) {
                    HrExamDetails hrExamDetails = hrExamDetailsList.get(0);
                    hrExamDetails.setIsPassed(hrExamResult.getExamResult());
                    hrExamDetailsRepository.updateById(hrExamDetails);
                }
            }
            if (hrExamResult.getPhysicalExaminationResult() != null) {
                //对应面试环节 1笔试 2 面试 7 考察  8体检
                QueryWrapper<HrExamDetails> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("exam_result_id", hrExamResult.getId());
                queryWrapper.eq("exam_type", 8);
                List<HrExamDetails> hrExamDetailsList = hrExamDetailsRepository.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(hrExamDetailsList)) {
                    HrExamDetails hrExamDetails = hrExamDetailsList.get(0);
                    hrExamDetails.setIsPassed(hrExamResult.getPhysicalExaminationResult());
                    hrExamDetailsRepository.updateById(hrExamDetails);
                }
            }
        }
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.EXAM_RESULT.getValue(),
            BusinessTypeEnum.UPDATE.getKey(),
            JSON.toJSONString(hrExamResultDTO),
            HrExamResultDTO.class,
            null,
            null,
            JSON.toJSONString(hrExamResultDTO),
            null,
            HrExamResultDTO.class
        );
    }


    private void saveData(ExcelImportResult<HrExamResultImport> result, String key, String paperId, String professionName, String examName) {
        int listSize = result.getList().size();
        int scale = 0;
        for (HrExamResultImport hrExamResultImport : result.getList()) {
            HrExamResult hrExamResult = new HrExamResult();
            try {
                BeanUtils.copyProperties(hrExamResultImport, hrExamResult);
//                //考查结果 和体检结果
//                Integer physicalResult = hrExamResultImport.getPhysicalExaminationResult();
//                if (physicalResult!=null){
//                    if (physicalResult!=0&&physicalResult!=1){
//                        throw new CommonException("如果有体检结果，只能是合格或不合格");
//                    }
//                }
//                Integer examResult1 = hrExamResultImport.getExamResult();
//                if (examResult1!=null){
//                    if (examResult1!=0&&examResult1!=1){
//                        throw new CommonException("如果有考察结果，只能是合格或不合格");
//                    }
//                }
                //通过身份证判断员工是否存在
                QueryWrapper<HrTalentStaff> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("certificate_num", hrExamResultImport.getCard());
                List<HrTalentStaff> talentStaffList = hrTalentStaffService.list(queryWrapper);
                if (CollectionUtils.isEmpty(talentStaffList)) {
                    throw new CommonException("系统中不存在该员工");
                }
                //获取员工id
                HrTalentStaff hrTalentStaff = talentStaffList.get(0);
                hrExamResult.setStaffId(hrTalentStaff.getId());
                //单独处理时间
//                try {
//                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
//                    if (hrExamResultImport.getWrittenExamStartTime()!=null){
//                        LocalDateTime writtenTime = LocalDateTime.parse(hrExamResultImport.getWrittenExamStartTime(), fmt);
//                        hrExamResult.setWrittenExamStartTime(writtenTime);
//                    }
//                    if (hrExamResultImport.getInterviewExamStartTime()!=null){
//                        LocalDateTime interviewTime = LocalDateTime.parse(hrExamResultImport.getInterviewExamStartTime(), fmt);
//                        hrExamResult.setInterviewExamStartTime(interviewTime);
//                    }
//                } catch (Exception e) {
//                    throw new CommonException("笔试时间或面试时间格式不正确");
//                }

                //通过客户名称获取客户id
                QueryWrapper<HrClient> queryWrapperClient = new QueryWrapper<>();
                queryWrapperClient.eq("client_name", hrExamResultImport.getClientName());
                List<HrClient> hrClientList = hrClientService.list(queryWrapperClient);
                if (CollectionUtils.isEmpty(hrClientList)) {
                    throw new CommonException("系统中不存在该单位名称");
                }
                String clientId = hrClientList.get(0).getId();
                hrExamResult.setClientId(clientId);
                //赋值岗位
                HashMap<String, String> stationMap = getStationMap();
                String stationId = stationMap.get(professionName);
                if (StringUtils.isBlank(stationId)) {
                    log.error("导入考试结果发现该{}岗位系统中已经不存在", professionName);
                } else {
                    hrExamResult.setStationId(stationId);
                }
                //添加到应试经历
                //HrStaffInterview hrStaffInterview = new HrStaffInterview();
                //分数
                //hrStaffInterview.setInterviewGrade(hrExamResultImport.getScore());
                hrExamResult.setInterviewLink(1);
                //hrStaffInterview.setInterviewEvaluate(hrExamResultImport.getEvaluation());
                //根据试卷id获取试卷及格线
                int passLine = hrPaperManagementService.getById(paperId).getPassLine();
                //判断笔试成绩是否为空
                int scoreResult = 0;
                if (hrExamResultImport.getScore() != null) {
                    int score = hrExamResultImport.getScore().intValue();
                    if (score >= passLine) {
                        scoreResult = 1;
                        hrExamResult.setInterviewResult(1);
                    } else {
                        hrExamResult.setInterviewResult(2);
                    }
                }
                //赋值面试平均成绩
                String interviewScore = hrExamResultImport.getInterviewScore();
                if (StringUtils.isNotBlank(interviewScore)) {
                    String regex = ",|，|\\s+";
                    List<String> scoreList = Arrays.asList((interviewScore.split(regex)));
                    //定义面试平均成绩
                    int parseInt = 0;
                    for (String score : scoreList) {
                        try {
                            parseInt += Integer.parseInt(score);
                        } catch (NumberFormatException e) {
                            throw new CommonException("请正确填写面试考官打分，多个考官请已逗号分割");
                        }
                    }
                    BigDecimal bigDecimal = new BigDecimal(parseInt);
                    BigDecimal size = new BigDecimal(scoreList.size());
                    BigDecimal divide = bigDecimal.divide(size, 2, BigDecimal.ROUND_DOWN);
                    hrExamResult.setInterviewScoreResult(divide);
                }
//                //赋值最终成绩
//                //现根据招聘简章获取招聘简章id
//                if (StringUtils.isNotBlank(examName) && StringUtils.isNotBlank(professionName)) {
//                    QueryWrapper<HrRecruitmentBrochure> hrRecruitmentBrochureQueryWrapper = new QueryWrapper<>();
//                    hrRecruitmentBrochureQueryWrapper.eq("recruit_brochure_name", examName);
//                    List<HrRecruitmentBrochure> list = hrRecruitmentBrochureService.list(hrRecruitmentBrochureQueryWrapper);
//                    HrRecruitmentBrochure hrRecruitmentBrochure =null;
//                    if (CollectionUtils.isNotEmpty(list)){
//                        hrRecruitmentBrochure = list.get(0);
//                    }
//                    //如果该招聘简章存在
//                    if (hrRecruitmentBrochure != null) {
//                        //获取该报名简章下该岗位该员工是否存在
//                        QueryWrapper<HrRegistrationDetails> hrRegistrationDetailsQueryWrapper = new QueryWrapper<>();
//                        hrRegistrationDetailsQueryWrapper.eq("brochure_id", hrRecruitmentBrochure.getId());
//                        hrRegistrationDetailsQueryWrapper.eq("station_name",professionName);
//                        hrRegistrationDetailsQueryWrapper.eq("staff_id", hrTalentStaff.getId());
//                        List<HrRegistrationDetails> hrRegistrationDetails = hrRegistrationDetailsRepository.selectList(hrRegistrationDetailsQueryWrapper);
//                        if (CollectionUtils.isEmpty(hrRegistrationDetails)){
//                            throw new CommonException("该人员不存在报名情况");
//                        }
//                        String hrRecruitmentBrochureId = hrRecruitmentBrochure.getId();
//                        //获取该简章该岗位下数据
//                        QueryWrapper<HrRecruitmentStation> hrRecruitmentStationQueryWrapper = new QueryWrapper<>();
//                        hrRecruitmentStationQueryWrapper.eq("service_id", hrRecruitmentBrochureId);
//                        hrRecruitmentStationQueryWrapper.eq("recruitment_station_name", professionName);
//                        List<HrRecruitmentStation> hrRecruitmentStationList = hrRecruitmentStationRepository.selectList(hrRecruitmentStationQueryWrapper);
//                        HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationList.get(0);
//                        //考试形式
//                        Integer examFormat = hrRecruitmentStation.getExamFormat();
//                        //面试权重
//                        BigDecimal interviewScoreWeight = hrRecruitmentStation.getInterviewScoreWeight();
//                        //笔试权重
//                        BigDecimal writtenScoreWeight = hrRecruitmentStation.getWrittenScoreWeight();
//                        //现根据考试形式来判断如何计算最终成绩
//                        if (examFormat == RecruitmentBrochure.ExamFormat.WRITTEN_EXAMINATION.getKey()) {
//                            BigDecimal score = hrExamResultImport.getScore();
//                            if (score != null) {
//                                BigDecimal multiply = score.multiply(writtenScoreWeight);
//                                hrExamResult.setFinalResult(multiply);
//                            }
//                        } else if (examFormat == RecruitmentBrochure.ExamFormat.INTERVIEW.getKey()) {
//                            BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
//                            if (interviewScoreResult != null) {
//                                BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
//                                hrExamResult.setFinalResult(multiply);
//                            }
//                        } else {
//                            BigDecimal score = hrExamResultImport.getScore();
//                            BigDecimal interviewScoreResult = hrExamResult.getInterviewScoreResult();
//                            if (interviewScoreResult != null && score != null) {
//                                BigDecimal multiply = interviewScoreResult.multiply(interviewScoreWeight);
//                                BigDecimal bigDecimal = score.multiply(writtenScoreWeight);
//                                BigDecimal add = multiply.add(bigDecimal);
//                                hrExamResult.setFinalResult(add);
//                            }
//                        }
//                        //添加考试结果的日志
//                        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
//                        //笔试成绩添加日志
//                        if (hrExamResultImport.getScore() != null) {
//                            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getUserName() + "录入了" + hrTalentStaff.getName() + "笔试成绩--" + hrExamResultImport.getScore(), null, ServiceCenterEnum.SIGN_UP.getKey());
//                        }
//                        //面试成绩添加日志
//                        if (hrExamResult.getInterviewScoreResult() != null) {
//                            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getUserName() + "录入了" + hrTalentStaff.getName() + "面试平均成绩--" + hrExamResult.getInterviewScoreResult(), null, ServiceCenterEnum.SIGN_UP.getKey());
//                        }
//                        //加试成绩添加日志
//                        if (hrExamResultImport.getAddResult() != null) {
//                            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getUserName() + "录入了" + hrTalentStaff.getName() + "加试成绩--" + hrExamResultImport.getAddResult(), null, ServiceCenterEnum.SIGN_UP.getKey());
//                        }
//                        //考察添加日志
//                        if (hrExamResultImport.getExamResult() != null) {
//                            Integer examResult = hrExamResultImport.getExamResult();
//                            String results = "";
//                            if (examResult == 1) {
//                                results = "合格";
//                            } else {
//                                results = "不合格";
//                            }
//                            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getUserName() + "录入了" + hrTalentStaff.getName() + "考查结果--" + results, null, ServiceCenterEnum.SIGN_UP.getKey());
//                        }
//                        //体检结果日志
//                        if (hrExamResultImport.getPhysicalExaminationResult() != null) {
//                            Integer physicalExaminationResult = hrExamResultImport.getPhysicalExaminationResult();
//                            String results = "";
//                            if (physicalExaminationResult == 1) {
//                                results = "合格";
//                            } else {
//                                results = "不合格";
//                            }
//                            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrRegistrationDetails.get(0).getId(), hrTalentStaff.getId(), jwtUserDTO.getId(), jwtUserDTO.getUserName() + "录入了" + hrTalentStaff.getName() + "体检结果--" + results, null, ServiceCenterEnum.SIGN_UP.getKey());
//                        }
//                        //修改报名情况状态
//                        if (StringUtils.isNotBlank(professionName) && StringUtils.isNotBlank(hrRecruitmentBrochureId)) {
//                            updateTyde(professionName, hrRecruitmentBrochureId, examName,hrTalentStaff.getId());
//                        }
//                    }
//                }


                //
                //hrStaffInterview.setInterviewTime(hrExamResultImport.getExamDate());
                //hrStaffInterview.setInterviewUnit(hrExamResultImport.getClientName());
                // hrStaffInterview.setClientId(clientId);
                //hrStaffInterview.setStaffId(hrTalentStaff.getId());
                //hrStaffInterview.setInterviewStationId(stationId);
                //hrStaffInterviewService.save(hrStaffInterview);
                hrExamResult.setExamType(0);
                hrExamResult.setPaperId(paperId);
                hrExamResult.setExamName(examName);
                hrExamResult.setProfessionName(professionName);
                //判断时新增还是修改
                QueryWrapper<HrExamResult> qw = new QueryWrapper<>();
                qw.eq("paper_id", paperId);
                qw.eq("profession_name", professionName);
                qw.eq("exam_name", examName);
                qw.eq("staff_id", hrTalentStaff.getId());
                List<HrExamResult> list = list(qw);
                if (CollectionUtils.isEmpty(list)) {
                    save(hrExamResult);
                } else {
                    hrExamResult.setId(list.get(0).getId());
                    updateById(hrExamResult);
                }
                //将对应的成绩录入
                //对应面试环节 1笔试 2 面试 7 考察  8体检
                //笔试
                if (hrExamResultImport.getScore() != null) {
                    String writtenLocation = hrExamResultImport.getWrittenLocation();
                    String writtenExamStartTime = hrExamResultImport.getWrittenExamStartTime();
                    addExamDetails(hrExamResultImport.getScore(), hrExamResult, scoreResult, writtenLocation, writtenExamStartTime, 1);
                }
                //面试
                if (hrExamResult.getInterviewScoreResult() != null && hrExamResultImport.getInterviewResult() != null) {
                    String interviewLocation = hrExamResultImport.getInterviewLocation();
                    String interviewExamStartTime = hrExamResultImport.getInterviewExamStartTime();
                    Integer interviewResult = hrExamResultImport.getInterviewResult();
                    addExamDetails(hrExamResult.getInterviewScoreResult(), hrExamResult, interviewResult, interviewLocation, interviewExamStartTime, 2);
                }
                //考察
                if (hrExamResultImport.getExamResult() != null) {
                    String surveyExamStartTime = hrExamResultImport.getSurveyExamStartTime();
                    String surveyLocation = hrExamResultImport.getSurveyLocation();
                    addExamDetails(null, hrExamResult, hrExamResultImport.getExamResult(), surveyLocation, surveyExamStartTime, 7);
                }
                //体检
                if (hrExamResultImport.getPhysicalExaminationResult() != null) {
                    String physicalExamStartTime = hrExamResultImport.getPhysicalExamStartTime();
                    String physicalLocation = hrExamResultImport.getPhysicalLocation();
                    addExamDetails(null, hrExamResult, hrExamResultImport.getPhysicalExaminationResult(), physicalLocation, physicalExamStartTime, 8);
                }
            } catch (Exception e) {
                log.error("保存考试结果异常:{}", e.getMessage());
                hrExamResultImport.setErrorMsg(e.getMessage());
            } finally {
                scale++;
                int i = CalculateUtils.calculationProgress(scale, listSize);
                redisCache.setCacheObject(key, i, 10, TimeUnit.MINUTES);
            }

        }
        if (StringUtils.isNotBlank(paperId)) {
            examNumber(paperId, professionName, examName);
        }

    }

    private void addExamDetails(BigDecimal score, HrExamResult hrExamResult, int scoreResult, String writtenLocation, String writtenExamStartTime, int examType) {
        HrExamDetails hrExamDetails = new HrExamDetails();
        //笔试
        hrExamDetails.setExamType(examType);
        hrExamDetails.setIsPassed(scoreResult);
        hrExamDetails.setScore(score);
        hrExamDetails.setExamResultId(hrExamResult.getId());
        if (StringUtils.isNotBlank(writtenLocation)) {
            hrExamDetails.setExamPlace(writtenLocation);
        }
        if (StringUtils.isNotBlank(writtenExamStartTime)) {
            try {
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                LocalDateTime writtenTime = LocalDateTime.parse(writtenExamStartTime, fmt);
                hrExamDetails.setExamTime(writtenTime);
            } catch (Exception e) {
                //对应面试环节 1笔试 2 面试 7 考察  8体检
                if (examType == 1) {
                    throw new CommonException("笔试时间格式不正确，请重新进行输入");
                }
                if (examType == 2) {
                    throw new CommonException("面试时间格式不正确，请重新进行输入");
                }
                if (examType == 7) {
                    throw new CommonException("考察时间格式不正确，请重新进行输入");
                }
                if (examType == 8) {
                    throw new CommonException("体检时间格式不正确，请重新进行输入");
                }
            }
        }
        QueryWrapper<HrExamDetails> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("exam_result_id", hrExamResult.getId());
        queryWrapper.eq("exam_type", examType);
        List<HrExamDetails> hrExamDetailsList = hrExamDetailsRepository.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(hrExamDetailsList)) {
            String id = hrExamDetailsList.get(0).getId();
            hrExamDetails.setId(id);
            hrExamDetailsRepository.updateById(hrExamDetails);
        } else {
            hrExamDetailsRepository.insert(hrExamDetails);
        }
    }

    /*  修改状态
     *  传岗位名称
     * 招聘简章的id
     * */
    public void updateTyde(String stationName, String brochureId, String brochureName, String staffId) {
        QueryWrapper<HrRecruitmentStation> qw = new QueryWrapper<>();
        QueryWrapper<HrRegistrationDetails> qws = new QueryWrapper<>();
        QueryWrapper<HrRecruitmentBrochure> qwb = new QueryWrapper<>();
    /*    //不过的集合
        List<Integer> statusList = new ArrayList<>();
        statusList.add(1);
        statusList.add(3);
        statusList.add(5);
        statusList.add(8);
        statusList.add(11);
        statusList.add(14);
        QueryWrapper<HrRegistrationDetails> ew = new QueryWrapper<>();
        ew.eq("staff_id", staffId);
        ew.eq("brochure_id", brochureId);
        ew.eq("station_name", stationName);
        ew.orderByDesc("created_date");
        ew.last("limit 1");
        HrRegistrationDetails hrRegistrationDetailsa = this.hrRegistrationDetailsRepository.selectOne(ew);
        Integer statusSum=hrRegistrationDetailsa.getStatus();
        if (statusList.contains(statusSum)) {
        } else {*/
        qw.eq("recruitment_station_name", stationName);
        qw.eq("service_id", brochureId);
        List<HrRecruitmentStation> hrRecruitmentStationList = this.hrRecruitmentStationRepository.selectList(qw);
        HrRecruitmentStation hrRecruitmentStation = hrRecruitmentStationList.get(0);
        //获取考试形式
        Integer examFormatsum = hrRecruitmentStation.getExamFormat();
        //获取晋级比例
        BigDecimal promotedRatio = hrRecruitmentStation.getPromotedRatio();
        //获取考察比例
        BigDecimal investigationRatio = hrRecruitmentStation.getInvestigationRatio();
        //获取是否等额考察
        Boolean IsEqualInvestigate = hrRecruitmentStation.getIsEqualInvestigate();
        //获取招聘人数
        Integer numberSum = hrRecruitmentStation.getRecruitmentPeopleNumber();
        BigDecimal numberSums = new BigDecimal(Integer.parseInt(numberSum.toString()));
        //获取面试及格线
        BigDecimal interviewPassLine = hrRecruitmentStation.getInterviewPassLine();
        //获取笔试及格线
        BigDecimal writtenPassLine = hrRecruitmentStation.getWrittenPassLine();
        //面试权重
        BigDecimal interviewScoreWeight = hrRecruitmentStation.getInterviewScoreWeight();
        //笔试权重
        BigDecimal writtenScoreWeight = hrRecruitmentStation.getWrittenScoreWeight();
        //获取岗位报名的人
        qws.eq("station_name", stationName);
        qws.eq("staff_id", staffId);
        qws.eq("brochure_id", brochureId);
        List<HrRegistrationDetails> HrRegistrationDetailsList = this.hrRegistrationDetailsRepository.selectList(qws);
        //获取合格人员id
        List<String> StaffIdList = new ArrayList<>();
        //获取不合格人员id
        List<String> noStaffIdList = new ArrayList<>();
        //查询简章id
        qwb.eq("recruit_brochure_name", brochureName);
        HrRecruitmentBrochure hrRecruitmentBrochure = this.hrRecruitmentBrochureRepository.selectOne(qwb);

        for (HrRegistrationDetails hrRegistrationDetails : HrRegistrationDetailsList) {
            QueryWrapper<HrExamResult> qwe = new QueryWrapper<>();
            //获取成绩
            qwe.eq("staff_id", hrRegistrationDetails.getStaffId());
            qwe.eq("profession_name", stationName);
            qwe.eq("exam_name", brochureName);
            List<HrExamResult> hrExamResults = this.hrExamResultRepository.selectList(qwe);
            HrExamResult hrExamResult = null;
            if (CollectionUtils.isNotEmpty(hrExamResults)) {
                hrExamResult = hrExamResults.get(0);
            }

            if (hrExamResult != null) {


                //判断考试形式  笔试
                if (examFormatsum == 1) {
                    //获取笔试及格线<考试成绩
                    if (hrExamResult.getScore() != null) {
                        if (writtenPassLine.compareTo(hrExamResult.getScore()) < 1) {
                            StaffIdList.add(hrRegistrationDetails.getStaffId());
                            this.hrExamResultRepository.interviewUpdateType(StaffIdList, stationName, hrRecruitmentBrochure.getId());
                        } else {
                            noStaffIdList.add(hrRegistrationDetails.getStaffId());
                            this.hrExamResultRepository.noInterviewUpdateType(noStaffIdList);
                        }
                    }
                }
                //判断考试形式  面试
                if (examFormatsum == 2) {
                    //获取面试及格线<考试成绩
                    if (interviewPassLine.compareTo(hrExamResult.getInterviewScoreResult()) < 1) {
                        //计算进面的人数
                        int peoplesum = investigationRatio.multiply(numberSums).intValue();
                        //进面的全部人员
                        StaffIdList.add(hrRegistrationDetails.getStaffId());
                        List<HrExamResult> WrittenID = this.hrExamResultRepository.selectWrittenPassLine(StaffIdList, peoplesum, stationName, brochureName);
                        if (WrittenID.size() > peoplesum) {
                            throw new CommonException("面试成绩需手动确认进考察的人员");
                        } else {
                            for (HrExamResult examResult : WrittenID) {
                                this.hrExamResultRepository.writtenwUpdateType(examResult.getProfessionName(), examResult.getStaffId(), hrRecruitmentBrochure.getId());
                            }

                        }
                   /* StaffIdList.removeAll(WrittenID);
                    this.hrExamResultRepository.noWrittenUpdateType(StaffIdList);*/
                    } else {
                        noStaffIdList.add(hrRegistrationDetails.getStaffId());
                        this.hrExamResultRepository.noWrittenUpdateType(noStaffIdList);
                    }
                }
                //判断考试形式 先面试后笔试
                if (examFormatsum == 3) {
                    if (hrExamResult.getInterviewScoreResult() != null) {
                        if (interviewPassLine != null) {
                            //获取面试及格线<=考试成绩
                            if (interviewPassLine.compareTo(hrExamResult.getInterviewScoreResult()) < 1) {
                                //计算进面的人数
                                int peoplesum = investigationRatio.multiply(numberSums).intValue();
                                //进面的全部人员
                                StaffIdList.add(hrRegistrationDetails.getStaffId());
                                List<HrExamResult> WrittenID = this.hrExamResultRepository.selectWrittenPassLine(StaffIdList, peoplesum, stationName, brochureName);

                                if (WrittenID.size() > peoplesum) {
                                    throw new CommonException("面试成绩需手动确认进考察的人员");
                                } else {
                                    for (HrExamResult examResult : WrittenID) {
                                        this.hrExamResultRepository.writtenwUpdateTypeS(examResult.getProfessionName(), examResult.getStaffId(), hrRecruitmentBrochure.getId());
                                    }
                                }
                  /*  StaffIdList.removeAll(WrittenID);
                    this.hrExamResultRepository.noWrittenUpdateType(StaffIdList);*/
                            } else {
                                noStaffIdList.add(hrRegistrationDetails.getStaffId());
                                this.hrExamResultRepository.noWrittenUpdateType(noStaffIdList);
                            }
                        }
                    }


                    //获取笔试及格线<=考试成绩
                    if (hrExamResult.getScore() != null) {
                        if (writtenPassLine.compareTo(hrExamResult.getScore()) < 1) {
                            StaffIdList.add(hrRegistrationDetails.getStaffId());
                            this.hrExamResultRepository.interviewUpdateType(StaffIdList, stationName, hrRecruitmentBrochure.getId());
                        } else {
                            noStaffIdList.add(hrRegistrationDetails.getStaffId());
                            this.hrExamResultRepository.noInterviewUpdateType(noStaffIdList);
                        }
                    }
                }
                //判断考试形式 先笔试后面试
                if (examFormatsum == 4) {
                    //获取笔试及格线<考试成绩
                    if (hrExamResult.getScore() != null) {
                        if (writtenPassLine.compareTo(hrExamResult.getScore()) < 1) {
                            StaffIdList.add(hrRegistrationDetails.getStaffId());
                            this.hrExamResultRepository.interviewUpdateTypeS(StaffIdList, stationName, hrRecruitmentBrochure.getId());
                        } else {
                            noStaffIdList.add(hrRegistrationDetails.getStaffId());
                            this.hrExamResultRepository.noInterviewUpdateType(noStaffIdList);
                        }
                    }
                    if (hrExamResult.getInterviewScoreResult() != null) {
                        if (interviewPassLine != null) {
                            //获取面试及格线<=考试成绩
                            if (interviewPassLine.compareTo(hrExamResult.getInterviewScoreResult()) < 1) {
                                //计算进面的人数
                                int peoplesum = investigationRatio.multiply(numberSums).intValue();
                                //进面的全部人员
                                StaffIdList.add(hrRegistrationDetails.getStaffId());
                                List<HrExamResult> WrittenID = this.hrExamResultRepository.selectWrittenPassLine(StaffIdList, peoplesum, stationName, brochureName);
                                if (WrittenID.size() > peoplesum) {
                                    throw new CommonException("面试成绩需手动确认进考察的人员");
                                } else {
                                    for (HrExamResult examResult : WrittenID) {
                                        this.hrExamResultRepository.writtenwUpdateType(examResult.getProfessionName(), examResult.getStaffId(), hrRecruitmentBrochure.getId());
                                    }
                                }

                                /* *//* StaffIdList.removeAll(WrittenID);*//*
                    this.hrExamResultRepository.noWrittenUpdateType(StaffIdList);*/
                            } else {
                                noStaffIdList.add(hrRegistrationDetails.getStaffId());
                                this.hrExamResultRepository.noWrittenUpdateType(noStaffIdList);
                            }

                        }
                    }

                }


                //待通知体检
                if (hrExamResult.getExamResult() != null) {
                    String type = "";
                    if (hrExamResult.getExamResult() == 1) {
                        type = "12";
                        this.hrExamResultRepository.updateExamResult(hrRegistrationDetails.getStaffId(), type, stationName, hrRecruitmentBrochure.getId());
                    } else {
                        type = "11";
                        this.hrExamResultRepository.updateExamResult(hrRegistrationDetails.getStaffId(), type, stationName, hrRecruitmentBrochure.getId());
                    }
                }
                //待通知待拟聘公式
                if (hrExamResult.getPhysicalExaminationResult() != null) {
                    String type = "";
                    if (hrExamResult.getPhysicalExaminationResult() == 1) {
                        type = "15";
                        this.hrExamResultRepository.updateExamResult(hrRegistrationDetails.getStaffId(), type, stationName, hrRecruitmentBrochure.getId());
                    } else {
                        type = "14";
                        this.hrExamResultRepository.updateExamResult(hrRegistrationDetails.getStaffId(), type, stationName, hrRecruitmentBrochure.getId());
                    }
                }
                //待转入职

                if (hrRegistrationDetails.getPublicityDate() != null && hrRegistrationDetails.getPublicityDateEnd() != null) {
                    //获取当前日期
                    DateFormat newtime = new SimpleDateFormat("yyyy-MM-dd");
                    LocalDate newdate = LocalDate.now();
                    String type = "";
                    if (hrRegistrationDetails.getPublicityDateEnd().isBefore(newdate) && hrRegistrationDetails.getPublicityDateEnd().equals(newdate)) {
                        type = "16";
                        this.hrExamResultRepository.updateExamResult(hrRegistrationDetails.getStaffId(), stationName, hrRecruitmentBrochure.getId(), type);
                    }
                }


            }
        }

/*

        List<HrExamResult>list=this.hrExamResultRepository.selectLists(stationName, hrRecruitmentBrochure.getId());

*/


    }

    /**
     * 重新计算考试人数和通过率
     *
     * @param paperId
     */
    private void examNumber(String paperId, String professionName, String examName) {
        //获取试卷的及格线
        HrPaperManagement hrPaperManagement = hrPaperManagementService.getById(paperId);
        //重新计算考试人数和通过率
        QueryWrapper<HrExamResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("paper_id", paperId);
        queryWrapper.eq(StringUtils.isNotBlank(professionName), "profession_name", professionName);
        queryWrapper.eq(StringUtils.isNotBlank(examName), "exam_name", examName);
        //查询试卷的考试人数
        int totalCount = count(queryWrapper);
        //查询试卷的及格人数
        queryWrapper.ge("score", hrPaperManagement.getPassLine());
        int passLineCount = count(queryWrapper);
        //修改试卷的考试人数和通过率
        HrExam hrExam = new HrExam();
        hrExam.setExamsNumber(totalCount);
        //计算通过率
        if (totalCount != 0) {
            BigDecimal bigDecimal = new BigDecimal((float) passLineCount / totalCount).setScale(2, RoundingMode.HALF_UP);
            hrExam.setExamsPassingRate(bigDecimal);
        } else {
            hrExam.setExamsPassingRate(new BigDecimal("0"));
        }
        QueryWrapper<HrExam> qw = new QueryWrapper<>();
        qw.eq("paper_id", paperId);
        qw.eq(StringUtils.isNotBlank(professionName), "profession_name", professionName);
        qw.eq(StringUtils.isNotBlank(examName), "exam_name", examName);
        hrExamService.update(hrExam, qw);
    }

    /**
     * 获取所有岗位名称
     *
     * @return
     */
    private HashMap<String, String> getStationMap() {
        HashMap<String, String> hashMap = new HashMap<>();
        for (HrStationDTO hrStationDTO : hrStationService.selectList()) {
            hashMap.put(hrStationDTO.getProfessionName(), hrStationDTO.getId());
        }
        return hashMap;
    }
}
